#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
表格选择和快捷键处理模块
提供对QTableWidget的多选、复制、粘贴和删除功能
"""

from PyQt5.QtWidgets import QTableWidget, QApplication, QTableWidgetItem, QAbstractItemView, QMessageBox, QComboBox
from PyQt5.QtCore import Qt, QObject, QEvent, QTimer, QItemSelectionModel
import logging
import csv
import io
from src.utils.global_clipboard_manager import clipboard_manager


class TableSelectionHandler(QObject):
    """表格选择和快捷键处理类"""

    def __init__(self, table_widget):
        """
        初始化

        参数:
            table_widget: 需要处理的QTableWidget对象
        """
        super().__init__(table_widget)
        self.logger = logging.getLogger(__name__)
        self.table = table_widget
        self.selected_cells = []  # 存储被选中的单元格坐标
        # 移除实例级别的剪贴板数据，改用全局剪贴板管理器
        # self.clipboard_data = []  # 存储复制的数据
        self.last_selected_cell = None  # 存储最后选中的单元格，用于Shift多选

        # 🔧 关键修复：设置选择行为为SelectItems，支持单元格级别的批量操作
        # 这样可以确保QComboBox控件所在的单元格能够被正确选中和操作
        self.table.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.table.setSelectionBehavior(QAbstractItemView.SelectItems)  # 修改为选择单元格

        # 安装事件过滤器以处理键盘事件
        self.table.installEventFilter(self)

        # 连接单元格点击信号，用于跟踪选择状态
        self.table.cellClicked.connect(self.on_cell_clicked)

        # 🔧 新增：安装QComboBox选择行为修复器
        self._install_combobox_selection_fix()

        # 连接选择变化信号，用于记录多选状态
        self.table.itemSelectionChanged.connect(self.on_selection_changed)

        # 🔧 关键修复：设置编辑触发器，避免QComboBox单击直接进入编辑模式
        # 这样可以确保QComboBox单元格能够被正常选中，而不是直接编辑
        self.table.setEditTriggers(QTableWidget.DoubleClicked | QTableWidget.EditKeyPressed | QTableWidget.SelectedClicked)

    def on_cell_clicked(self, row, col):
        """
        处理单元格点击事件，记录最后选中的单元格

        参数:
            row: 行索引
            col: 列索引
        """
        # 检测键盘修饰键状态
        modifiers = QApplication.keyboardModifiers()
        ctrl_pressed = bool(modifiers & Qt.ControlModifier)
        shift_pressed = bool(modifiers & Qt.ShiftModifier)

        # 🔧 **关键修复**：只有在非Shift选择时才立即更新last_selected_cell
        # Shift选择需要保持原来的起始点
        old_last_cell = self.last_selected_cell
        if not shift_pressed:
            self.last_selected_cell = (row, col)

        # 🔧 **调试日志**：检查单元格类型
        cell_widget = self.table.cellWidget(row, col)
        cell_item = self.table.item(row, col)
        widget_type = "None"
        if cell_widget:
            widget_type = type(cell_widget).__name__
        elif cell_item:
            widget_type = "QTableWidgetItem"

        # 记录单元格点击和修饰键状态
        self.logger.info(f"🔧 单元格点击: 位置=({row},{col}) 类型={widget_type} "
                        f"Ctrl={ctrl_pressed} Shift={shift_pressed}")
        self.logger.debug(f"🔧 最后选中单元格状态: 原={old_last_cell} 当前={self.last_selected_cell}")

        # 如果按下了修饰键，记录多选操作
        if ctrl_pressed or shift_pressed:
            selected_cells = self.get_selected_cells()
            self.logger.info(f"🔧 多选操作: 当前选中 {len(selected_cells)} 个单元格")
            if len(selected_cells) <= 10:  # 避免日志过长
                self.logger.info(f"🔧 选中单元格列表: {selected_cells}")

            # 🔧 **调试日志**：特别记录Shift选择的起始点
            if shift_pressed:
                if old_last_cell:
                    self.logger.info(f"🔧 Shift选择: 起始点={old_last_cell} 结束点=({row},{col})")
                else:
                    self.logger.warning(f"🔧 Shift选择警告: 没有起始点，当前点击=({row},{col})")

        # 🔧 **关键修复**：在处理完选择逻辑后，根据选择类型更新last_selected_cell
        if shift_pressed:
            # Shift选择：保持原来的起始点不变（除非没有起始点）
            if old_last_cell is None:
                self.last_selected_cell = (row, col)
                self.logger.debug(f"🔧 Shift选择设置新起始点: {self.last_selected_cell}")
            else:
                # 保持原来的起始点
                self.logger.debug(f"🔧 Shift选择保持起始点: {self.last_selected_cell}")
        elif ctrl_pressed:
            # Ctrl选择：更新为当前点击的单元格
            self.last_selected_cell = (row, col)
            self.logger.debug(f"🔧 Ctrl选择更新起始点: {self.last_selected_cell}")
        # 普通选择已经在前面更新了

    def on_selection_changed(self):
        """
        处理选择变化事件，记录当前选中的单元格信息
        """
        selected_cells = self.get_selected_cells()
        if len(selected_cells) > 1:  # 只记录多选情况
            self.logger.info(f"选择变化: 当前选中 {len(selected_cells)} 个单元格")
            if len(selected_cells) <= 10:  # 避免日志过长
                self.logger.info(f"选中单元格详情: {selected_cells}")
            else:
                # 对于大量选择，只显示范围
                min_row = min(cell[0] for cell in selected_cells)
                max_row = max(cell[0] for cell in selected_cells)
                min_col = min(cell[1] for cell in selected_cells)
                max_col = max(cell[1] for cell in selected_cells)
                self.logger.info(f"选中范围: 行{min_row}-{max_row}, 列{min_col}-{max_col}")

    def eventFilter(self, watched_obj, event):
        """
        事件过滤器，处理键盘事件

        参数:
            watched_obj: 被监视的对象
            event: 事件对象

        返回:
            True表示事件已处理，False表示事件需要进一步处理
        """
        # 🔧 新增：处理QComboBox控件的特殊事件
        if hasattr(watched_obj, 'currentText') and hasattr(watched_obj, 'findText'):
            # 这是一个QComboBox控件
            # 🔧 **强化调试**：记录所有QComboBox事件
            event_type_name = {
                QEvent.MouseButtonPress: "MouseButtonPress",
                QEvent.MouseButtonRelease: "MouseButtonRelease",
                QEvent.MouseMove: "MouseMove",
                QEvent.FocusIn: "FocusIn",
                QEvent.FocusOut: "FocusOut",
                QEvent.KeyPress: "KeyPress",
                QEvent.KeyRelease: "KeyRelease"
            }.get(event.type(), f"Event_{event.type()}")

            if event.type() in [QEvent.MouseButtonPress, QEvent.MouseButtonRelease, QEvent.MouseMove]:
                combo_row, combo_col = self._find_widget_position(watched_obj)
                modifiers = QApplication.keyboardModifiers()
                ctrl_pressed = bool(modifiers & Qt.ControlModifier)
                shift_pressed = bool(modifiers & Qt.ShiftModifier)
                self.logger.info(f"🔧 QComboBox事件过滤器: {event_type_name} 位置=({combo_row},{combo_col}) "
                               f"Ctrl={ctrl_pressed} Shift={shift_pressed}")

            return self._handle_combobox_event(watched_obj, event)

        if watched_obj == self.table:
            # 处理键盘事件
            if event.type() == QEvent.KeyPress:
                # 记录键盘按键事件
                key_name = self._get_key_name(event.key())
                modifiers = event.modifiers()
                ctrl_pressed = bool(modifiers & Qt.ControlModifier)
                shift_pressed = bool(modifiers & Qt.ShiftModifier)
                alt_pressed = bool(modifiers & Qt.AltModifier)

                # 记录按键信息
                modifier_text = []
                if ctrl_pressed:
                    modifier_text.append("Ctrl")
                if shift_pressed:
                    modifier_text.append("Shift")
                if alt_pressed:
                    modifier_text.append("Alt")

                modifier_str = "+".join(modifier_text)
                # 取消按键类通知日志打印
                # if modifier_str:
                #     self.logger.info(f"按键事件: {modifier_str}+{key_name}")
                # else:
                #     self.logger.info(f"按键事件: {key_name}")

                # 复制快捷键(Ctrl+C)
                if event.key() == Qt.Key_C and event.modifiers() == Qt.ControlModifier:
                    self.copy_selected_cells()
                    return True

                # 粘贴快捷键(Ctrl+V)
                elif event.key() == Qt.Key_V and event.modifiers() == Qt.ControlModifier:
                    self.paste_to_selected_cells()
                    return True

                # 删除快捷键(Delete)
                elif event.key() == Qt.Key_Delete:
                    self.delete_selected_cells()
                    return True

                # 全选快捷键(Ctrl+A)
                elif event.key() == Qt.Key_A and event.modifiers() == Qt.ControlModifier:
                    self.select_all_cells()
                    return True

            # 处理鼠标事件，特别是带修饰键的点击
            elif event.type() == QEvent.MouseButtonPress:
                modifiers = QApplication.keyboardModifiers()
                ctrl_pressed = bool(modifiers & Qt.ControlModifier)
                shift_pressed = bool(modifiers & Qt.ShiftModifier)

                if ctrl_pressed or shift_pressed:
                    # 获取点击位置的单元格
                    item = self.table.itemAt(event.pos())
                    if item:
                        row, col = item.row(), item.column()
                        self.logger.info(f"鼠标点击(带修饰键): 行={row}, 列={col}, Ctrl={ctrl_pressed}, Shift={shift_pressed}")

        # 让QTableWidget自己处理其他事件，包括Ctrl和Shift多选
        return super().eventFilter(watched_obj, event)

    def _get_key_name(self, key):
        """
        获取按键名称

        参数:
            key: Qt按键代码

        返回:
            按键名称字符串
        """
        key_names = {
            Qt.Key_A: "A", Qt.Key_B: "B", Qt.Key_C: "C", Qt.Key_D: "D", Qt.Key_E: "E",
            Qt.Key_F: "F", Qt.Key_G: "G", Qt.Key_H: "H", Qt.Key_I: "I", Qt.Key_J: "J",
            Qt.Key_K: "K", Qt.Key_L: "L", Qt.Key_M: "M", Qt.Key_N: "N", Qt.Key_O: "O",
            Qt.Key_P: "P", Qt.Key_Q: "Q", Qt.Key_R: "R", Qt.Key_S: "S", Qt.Key_T: "T",
            Qt.Key_U: "U", Qt.Key_V: "V", Qt.Key_W: "W", Qt.Key_X: "X", Qt.Key_Y: "Y",
            Qt.Key_Z: "Z", Qt.Key_Delete: "Delete", Qt.Key_Backspace: "Backspace",
            Qt.Key_Enter: "Enter", Qt.Key_Return: "Return", Qt.Key_Space: "Space",
            Qt.Key_Tab: "Tab", Qt.Key_Escape: "Escape", Qt.Key_Up: "Up", Qt.Key_Down: "Down",
            Qt.Key_Left: "Left", Qt.Key_Right: "Right", Qt.Key_Home: "Home", Qt.Key_End: "End",
            Qt.Key_PageUp: "PageUp", Qt.Key_PageDown: "PageDown"
        }
        return key_names.get(key, f"Key_{key}")

    def get_selected_cells(self):
        """
        获取当前选中的单元格，支持QComboBox控件所在的单元格

        返回:
            选中的单元格坐标列表 [(row, col), ...]
        """
        selected_cells = []

        # 🔧 关键修复：支持SelectItems模式，正确处理包含QComboBox的单元格选择
        selection_model = self.table.selectionModel()
        if selection_model:
            # 获取所有选中的索引
            selected_indexes = selection_model.selectedIndexes()

            # 从选中的索引中提取单元格坐标
            for index in selected_indexes:
                if index.isValid():
                    selected_cells.append((index.row(), index.column()))

            # 如果没有通过索引获取到选中项，回退到selectedItems方法
            if not selected_cells:
                for item in self.table.selectedItems():
                    if item:
                        selected_cells.append((item.row(), item.column()))
        else:
            # 回退到原有方法
            for item in self.table.selectedItems():
                if item:
                    selected_cells.append((item.row(), item.column()))

        # 去重并按行列顺序排序，方便后续处理
        selected_cells = list(set(selected_cells))
        selected_cells.sort(key=lambda x: (x[0], x[1]))

        # 🔧 增强功能：验证选中的单元格，确保包含QComboBox的单元格也能被正确识别
        validated_cells = self._validate_selected_cells(selected_cells)

        return validated_cells

    def _validate_selected_cells(self, selected_cells):
        """
        验证选中的单元格，确保包含QComboBox控件的单元格也能被正确处理

        参数:
            selected_cells: 原始选中的单元格列表

        返回:
            验证后的单元格列表
        """
        validated_cells = []

        for row, col in selected_cells:
            # 检查单元格是否在有效范围内
            if (0 <= row < self.table.rowCount() and
                0 <= col < self.table.columnCount()):

                # 检查单元格是否包含控件（如QComboBox）
                cell_widget = self.table.cellWidget(row, col)
                cell_item = self.table.item(row, col)

                # 如果单元格包含控件或项目，则认为是有效的
                if cell_widget is not None or cell_item is not None:
                    validated_cells.append((row, col))

                    # 记录包含QComboBox的单元格
                    if cell_widget and hasattr(cell_widget, 'currentText'):
                        self.logger.debug(f"检测到QComboBox单元格: ({row}, {col})")
                else:
                    # 即使没有控件或项目，也添加到列表中（可能是空单元格）
                    validated_cells.append((row, col))

        return validated_cells

    def copy_selected_cells(self):
        """
        复制选中的单元格内容到剪贴板
        使用增强的单元格级别复制功能
        """
        selected_cells = self.get_selected_cells()
        if not selected_cells:
            return

        # 使用新的单元格级别复制
        success = self._copy_cells_enhanced(selected_cells)

        if not success:
            # 降级到原有的复制方法
            self.logger.warning("增强复制失败，降级到原有方法")
            self._copy_cells_legacy(selected_cells)

    def _copy_cells_enhanced(self, selected_cells):
        """
        增强的单元格级别复制功能

        参数:
            selected_cells: 选中的单元格列表

        返回:
            bool: 是否成功
        """
        try:
            # 导入增强的剪贴板管理器
            from .enhanced_clipboard_manager import enhanced_clipboard_manager
            from .cell_data_structures import CellData, SelectionData

            # 构建单元格数据
            cell_data_list = []
            for row, col in selected_cells:
                content = self._extract_cell_content(row, col)
                metadata = self._extract_cell_metadata(row, col)
                cell_data = CellData(row, col, content, metadata)
                cell_data_list.append(cell_data)

            # 创建选区数据
            source_info = self._get_source_info(len(selected_cells))
            selection_data = SelectionData(cell_data_list, source_info)

            # 存储到增强剪贴板
            success = enhanced_clipboard_manager.copy_cell_selection(selection_data)

            if success:
                self.logger.info(f"已使用增强方法复制 {len(selected_cells)} 个单元格")

            return success

        except Exception as e:
            self.logger.error(f"增强复制功能失败: {e}")
            return False

    def _extract_cell_content(self, row, col):
        """
        提取单元格内容，特别优化QComboBox控件的内容提取

        参数:
            row: 行索引
            col: 列索引

        返回:
            str: 单元格内容
        """
        try:
            # 🔧 优先检查单元格控件（如QComboBox）
            widget = self.table.cellWidget(row, col)
            if widget:
                # 🔧 增强QComboBox内容提取
                if hasattr(widget, 'currentText') and hasattr(widget, 'findText'):
                    # 这是一个QComboBox，获取当前选中的文本
                    current_text = widget.currentText()
                    self.logger.debug(f"从QComboBox提取内容 ({row}, {col}): '{current_text}'")
                    return current_text if current_text else ""

                # 处理其他类型的控件
                elif hasattr(widget, 'text'):
                    # QLineEdit等文本控件
                    text_content = widget.text()
                    self.logger.debug(f"从文本控件提取内容 ({row}, {col}): '{text_content}'")
                    return text_content if text_content else ""

                elif hasattr(widget, 'toPlainText'):
                    # QTextEdit等多行文本控件
                    plain_text = widget.toPlainText()
                    self.logger.debug(f"从多行文本控件提取内容 ({row}, {col}): '{plain_text}'")
                    return plain_text if plain_text else ""

                elif hasattr(widget, 'value'):
                    # QSpinBox等数值控件
                    value_content = str(widget.value())
                    self.logger.debug(f"从数值控件提取内容 ({row}, {col}): '{value_content}'")
                    return value_content

                else:
                    # 未知控件类型
                    self.logger.warning(f"未知控件类型 ({row}, {col}): {type(widget)}")
                    return ""

            # 🔧 如果没有控件，检查QTableWidgetItem
            item = self.table.item(row, col)
            if item:
                item_text = item.text()
                self.logger.debug(f"从表格项提取内容 ({row}, {col}): '{item_text}'")
                return item_text if item_text else ""

            # 如果既没有控件也没有项目，返回空字符串
            self.logger.debug(f"空单元格 ({row}, {col})")
            return ""

        except Exception as e:
            self.logger.error(f"提取单元格内容失败 ({row}, {col}): {e}")
            return ""

    def _extract_cell_metadata(self, row, col):
        """
        提取单元格元数据

        参数:
            row: 行索引
            col: 列索引

        返回:
            dict: 元数据
        """
        metadata = {
            'type': 'text',
            'format': 'plain'
        }

        try:
            # 获取列标题
            header_item = self.table.horizontalHeaderItem(col)
            if header_item:
                metadata['column_title'] = header_item.text()

            # 检查是否为特殊部件
            widget = self.table.cellWidget(row, col)
            if widget:
                widget_type = type(widget).__name__
                metadata['widget_type'] = widget_type

                # 根据部件类型设置元数据
                if 'ComboBox' in widget_type:
                    metadata['type'] = 'combobox'
                elif 'SpinBox' in widget_type:
                    metadata['type'] = 'number'
                elif 'DateEdit' in widget_type:
                    metadata['type'] = 'date'
                elif 'TextEdit' in widget_type:
                    metadata['type'] = 'multiline_text'

            return metadata

        except Exception as e:
            self.logger.error(f"提取单元格元数据失败 ({row}, {col}): {e}")
            return metadata

    def _get_source_info(self, cell_count):
        """
        获取数据来源信息

        参数:
            cell_count: 单元格数量

        返回:
            str: 来源信息
        """
        try:
            table_name = getattr(self.table.parent(), 'table_name', '未知表格') if self.table.parent() else '未知表格'
            return f"{table_name} ({cell_count}个单元格)"
        except:
            return f"表格 ({cell_count}个单元格)"

    def _copy_cells_legacy(self, selected_cells):
        """
        原有的复制方法（降级使用）

        参数:
            selected_cells: 选中的单元格列表
        """
        # 确定选中区域的边界
        min_row = min(cell[0] for cell in selected_cells)
        max_row = max(cell[0] for cell in selected_cells)
        min_col = min(cell[1] for cell in selected_cells)
        max_col = max(cell[1] for cell in selected_cells)

        # 创建二维数组存储数据
        rows = max_row - min_row + 1
        cols = max_col - min_col + 1
        data = [["" for _ in range(cols)] for _ in range(rows)]

        # 填充数据
        for row, col in selected_cells:
            content = self._extract_cell_content(row, col)
            # 存储到对应位置
            data[row - min_row][col - min_col] = content

        # 获取表格来源信息
        source_info = self._get_source_info(len(selected_cells))

        # 使用全局剪贴板管理器保存数据
        clipboard_manager.copy_data(data, source_info)

        self.logger.debug(f"已使用原有方法复制 {len(selected_cells)} 个单元格到全局剪贴板")

    def paste_to_selected_cells(self):
        """
        将剪贴板内容粘贴到选中的单元格
        使用增强的单元格级别粘贴功能
        """
        # 获取当前选中的单元格或当前单元格
        selected_cells = self.get_selected_cells()
        if not selected_cells:
            # 如果没有选中的单元格，使用当前单元格
            current_row = self.table.currentRow()
            current_col = self.table.currentColumn()
            if current_row < 0 or current_col < 0:
                return
            selected_cells = [(current_row, current_col)]

        # 尝试使用增强的粘贴功能
        success = self._paste_cells_enhanced(selected_cells)

        if not success:
            # 降级到原有的粘贴方法
            self.logger.warning("增强粘贴失败，降级到原有方法")
            self._paste_cells_legacy(selected_cells)

    def _paste_cells_enhanced(self, selected_cells):
        """
        增强的单元格级别粘贴功能

        参数:
            selected_cells: 选中的单元格列表

        返回:
            bool: 是否成功
        """
        try:
            self.logger.info(f"开始增强粘贴功能，目标单元格数量: {len(selected_cells)}")

            # 导入增强的剪贴板管理器
            from .enhanced_clipboard_manager import enhanced_clipboard_manager

            # 获取剪贴板数据
            self.logger.debug("正在获取剪贴板数据...")
            selection_data = enhanced_clipboard_manager.get_cell_selection()

            if not selection_data:
                self.logger.debug("剪贴板中没有选区数据")
                return False

            if not selection_data.cells:
                self.logger.debug("选区数据中没有单元格")
                return False

            self.logger.info(f"获取到剪贴板数据: {len(selection_data.cells)}个单元格")

            # 创建单元格映射
            self.logger.debug("正在创建单元格映射...")
            cell_mappings = enhanced_clipboard_manager.create_paste_mapping(selected_cells)
            if not cell_mappings:
                self.logger.warning("无法创建单元格映射")
                return False

            self.logger.info(f"创建了 {len(cell_mappings)} 个单元格映射")

            # 执行单元格级别粘贴
            self.logger.debug("正在执行单元格级别粘贴...")
            success = self._execute_cell_paste(cell_mappings)

            if success:
                self.logger.info(f"已使用增强方法粘贴 {len(cell_mappings)} 个单元格")
            else:
                self.logger.warning("单元格级别粘贴执行失败")

            return success

        except Exception as e:
            self.logger.error(f"增强粘贴功能失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False

    def _execute_cell_paste(self, cell_mappings):
        """
        执行单元格级别粘贴

        参数:
            cell_mappings: 单元格映射列表

        返回:
            bool: 是否成功
        """
        try:
            # 获取主窗口引用
            main_window = self._get_main_window()
            if not main_window:
                self.logger.error("无法获取主窗口引用")
                return False

            # 记录粘贴操作
            paste_operations = []
            modified_cells = []

            for mapping in cell_mappings:
                row, col = mapping.target_row, mapping.target_col
                content = mapping.source_cell.content

                # 检查目标单元格是否为特殊处理列
                if self._should_skip_cell(row, col):
                    continue

                # 获取列标题和问题ID
                column_title = self._get_column_title(col)
                problem_id = self._get_problem_id(row, main_window)

                # 执行粘贴操作
                success, old_value = self._paste_content_to_cell(row, col, content, column_title, main_window)
                if success:
                    modified_cells.append((row, col))
                    paste_operations.append({
                        'row': row, 'col': col, 'old_value': old_value, 'new_value': content,
                        'problem_id': problem_id, 'column_title': column_title
                    })

            # 更新数据库和触发事件
            if paste_operations:
                self._finalize_paste_operations(paste_operations, modified_cells, main_window)
                return True

            return False

        except Exception as e:
            self.logger.error(f"执行单元格粘贴失败: {e}")
            return False

    def _should_skip_cell(self, row, col):
        """
        检查是否应该跳过某个单元格

        参数:
            row: 行索引
            col: 列索引

        返回:
            bool: 是否跳过
        """
        try:
            # 检查ID列
            header_item = self.table.horizontalHeaderItem(col)
            if header_item and header_item.text() == "id":
                return True

            # 检查复选框列（假设第一列）
            if col == 0:
                return True

            return False

        except:
            return False

    def _get_column_title(self, col):
        """
        获取列标题

        参数:
            col: 列索引

        返回:
            str: 列标题
        """
        try:
            header_item = self.table.horizontalHeaderItem(col)
            return header_item.text() if header_item else f"列 {col}"
        except:
            return f"列 {col}"

    def _get_main_window(self):
        """
        获取主窗口引用

        返回:
            主窗口对象或None
        """
        try:
            # 尝试从表格的父级获取主窗口
            parent = self.table.parent()
            while parent:
                if hasattr(parent, 'update_problem_record'):
                    return parent
                parent = parent.parent()
            return None
        except:
            return None

    def _paste_content_to_cell(self, row, col, content, column_title, main_window):
        """
        将内容粘贴到指定单元格

        参数:
            row: 行索引
            col: 列索引
            content: 内容
            column_title: 列标题
            main_window: 主窗口引用

        返回:
            tuple: (是否成功, 原有值)
        """
        try:
            # 获取原有值
            old_value = self._extract_cell_content(row, col)

            # 设置新值
            item = self.table.item(row, col)
            if item:
                item.setText(content)
            else:
                # 创建新的表格项
                new_item = QTableWidgetItem(content)
                self.table.setItem(row, col, new_item)

            return True, old_value

        except Exception as e:
            self.logger.error(f"粘贴内容到单元格失败 ({row}, {col}): {e}")
            return False, ""

    def _get_problem_id(self, row, main_window):
        """
        获取问题ID

        参数:
            row: 行索引
            main_window: 主窗口引用

        返回:
            str: 问题ID
        """
        try:
            if main_window and hasattr(main_window, 'get_problem_id_by_row'):
                return main_window.get_problem_id_by_row(row)

            # 尝试从表格中获取ID列
            for col in range(self.table.columnCount()):
                header_item = self.table.horizontalHeaderItem(col)
                if header_item and header_item.text().lower() in ['id', 'problem_id', '问题编号']:
                    item = self.table.item(row, col)
                    if item:
                        return item.text()

            return f"row_{row}"

        except Exception as e:
            self.logger.error(f"获取问题ID失败 (row {row}): {e}")
            return f"row_{row}"

    def _finalize_paste_operations(self, paste_operations, modified_cells, main_window):
        """
        完成粘贴操作的后续处理（优化版本，减少卡顿）

        参数:
            paste_operations: 粘贴操作列表
            modified_cells: 修改的单元格列表
            main_window: 主窗口引用
        """
        try:
            self.logger.info(f"开始批量处理 {len(paste_operations)} 个粘贴操作")

            # 🔧 性能优化：暂时禁用表格信号，避免每次更新都触发事件
            old_signals_blocked = self.table.blockSignals(True)

            try:
                # 🔧 性能优化：批量更新数据库，按问题ID分组
                if main_window and hasattr(main_window, 'update_problem_record'):
                    # 按问题ID分组操作，减少数据库调用次数
                    problem_updates = {}
                    for operation in paste_operations:
                        problem_id = operation['problem_id']
                        column_title = operation['column_title']
                        new_value = operation['new_value']

                        if problem_id not in problem_updates:
                            problem_updates[problem_id] = {}
                        problem_updates[problem_id][column_title] = new_value

                    # 批量更新每个问题的多个字段
                    for problem_id, updates in problem_updates.items():
                        if hasattr(main_window, 'batch_update_problem_record'):
                            # 如果支持批量更新，使用批量更新
                            main_window.batch_update_problem_record(problem_id, updates)
                        else:
                            # 否则逐个更新
                            for column_title, new_value in updates.items():
                                main_window.update_problem_record(problem_id, column_title, new_value)

                    self.logger.info(f"批量更新了 {len(problem_updates)} 个问题记录")

            finally:
                # 恢复表格信号
                self.table.blockSignals(old_signals_blocked)

            # 🔧 性能优化：批量触发表格更新事件
            if modified_cells:
                # 只触发一次整体更新，而不是每个单元格都更新
                self.table.viewport().update()

                # 记录撤销操作（如果支持）
                if main_window and hasattr(main_window, 'record_batch_paste'):
                    main_window.record_batch_paste(paste_operations)

            # 记录日志
            self.logger.info(f"批量粘贴完成: {len(modified_cells)} 个单元格已更新")

        except Exception as e:
            self.logger.error(f"完成粘贴操作失败: {e}")
            # 确保信号状态恢复
            self.table.blockSignals(False)

    def _paste_cells_legacy(self, selected_cells):
        """
        原有的粘贴方法（降级使用）

        参数:
            selected_cells: 选中的单元格列表
        """
        # 在粘贴前强制刷新系统剪贴板，确保获取最新内容
        clipboard_manager.refresh_from_system_clipboard()

        # 从全局剪贴板管理器获取数据
        clipboard_data, source_info = clipboard_manager.get_clipboard_data()

        if not clipboard_data:
            self.logger.warning("剪贴板中没有可粘贴的数据")
            return

        # 获取主窗口引用
        main_window = None
        widget = self.table
        while widget:
            if hasattr(widget, 'problem_table') and widget.problem_table == self.table:
                main_window = widget
                break
            widget = widget.parent()

        # 🔧 新增：检查是否为单个单元格内容粘贴到多个选中单元格的情况
        clipboard_rows = len(clipboard_data)
        clipboard_cols = max(len(row) for row in clipboard_data) if clipboard_data else 0
        is_single_cell_to_multiple = (clipboard_rows == 1 and clipboard_cols == 1 and len(selected_cells) > 1)

        if is_single_cell_to_multiple:
            # 🔧 功能1：多单元格粘贴功能 - 将单个内容填充到所有选中的单元格
            self.logger.info(f"执行多单元格粘贴：将单个内容填充到 {len(selected_cells)} 个选中单元格")
            self._paste_single_content_to_multiple_cells(clipboard_data[0][0], selected_cells, main_window)
        else:
            # 原有的多行多列数据粘贴逻辑
            self._paste_multi_content_to_cells(clipboard_data, selected_cells, main_window)

    def _paste_single_content_to_multiple_cells(self, content, selected_cells, main_window):
        """
        将单个内容粘贴到多个选中的单元格

        参数:
            content: 要粘贴的内容
            selected_cells: 选中的单元格列表 [(row, col), ...]
            main_window: 主窗口引用
        """
        modified_cells = []
        paste_operations = []

        for row, col in selected_cells:
            # 检查目标单元格是否为特殊处理列(如ID列、复选框列等)
            header_item = self.table.horizontalHeaderItem(col)
            if header_item and header_item.text() == "id":
                continue  # 跳过ID列

            if col == 0:  # 假设第一列是复选框列
                continue

            # 获取列标题
            column_title = header_item.text() if header_item else f"列 {col}"

            # 获取问题ID
            problem_id = self._get_problem_id(row, main_window)

            # 执行粘贴操作
            success, old_value = self._paste_content_to_cell(row, col, content, column_title, main_window)
            if success:
                modified_cells.append((row, col))
                paste_operations.append({
                    'row': row, 'col': col, 'old_value': old_value, 'new_value': content,
                    'problem_id': problem_id, 'column_title': column_title
                })

        # 更新数据库和触发事件
        self._finalize_paste_operations(paste_operations, modified_cells, main_window)

    def _paste_multi_content_to_cells(self, clipboard_data, selected_cells, main_window):
        """
        将多行多列数据粘贴到表格（原有逻辑）

        参数:
            clipboard_data: 剪贴板数据
            selected_cells: 选中的单元格列表
            main_window: 主窗口引用
        """
        # 确定粘贴的起始位置(选中单元格中的左上角)
        start_row = min(cell[0] for cell in selected_cells)
        start_col = min(cell[1] for cell in selected_cells)

        # 验证和预处理剪贴板数据
        if not self._validate_clipboard_data(clipboard_data):
            return

        # 执行粘贴操作
        clipboard_rows = len(clipboard_data)
        clipboard_cols = max(len(row) for row in clipboard_data) if clipboard_data else 0

        # 检查表格大小并提供用户选择
        if start_row + clipboard_rows > self.table.rowCount() or start_col + clipboard_cols > self.table.columnCount():
            clipboard_data = self._handle_paste_size_conflict(clipboard_data, start_row, start_col, clipboard_rows, clipboard_cols)
            if clipboard_data is None:
                return
            # 更新行列数
            clipboard_rows = len(clipboard_data)
            clipboard_cols = max(len(row) for row in clipboard_data) if clipboard_data else 0

        # 记录修改的单元格和粘贴操作，以便触发变更事件和撤销
        modified_cells = []
        paste_operations = []

        # 执行粘贴
        for i in range(min(clipboard_rows, self.table.rowCount() - start_row)):
            for j in range(min(len(clipboard_data[i]), self.table.columnCount() - start_col)):
                row = start_row + i
                col = start_col + j
                value = clipboard_data[i][j]

                # 检查目标单元格是否为特殊处理列(如ID列、复选框列等)
                header_item = self.table.horizontalHeaderItem(col)
                if header_item and header_item.text() == "id":
                    continue  # 跳过ID列

                if col == 0:  # 假设第一列是复选框列
                    continue

                # 获取列标题
                column_title = header_item.text() if header_item else f"列 {col}"

                # 获取问题ID
                problem_id = self._get_problem_id(row, main_window)

                # 执行粘贴操作
                success, old_value = self._paste_content_to_cell(row, col, value, column_title, main_window)
                if success:
                    modified_cells.append((row, col))
                    paste_operations.append({
                        'row': row, 'col': col, 'old_value': old_value, 'new_value': value,
                        'problem_id': problem_id, 'column_title': column_title
                    })

        # 更新数据库和触发事件
        self._finalize_paste_operations(paste_operations, modified_cells, main_window)

    def _get_problem_id(self, row, main_window):
        """获取指定行的问题ID"""
        problem_id = None
        if main_window:
            id_col = -1
            for c in range(self.table.columnCount()):
                header = self.table.horizontalHeaderItem(c)
                if header and header.text() == "id":
                    id_col = c
                    break

            if id_col != -1:
                id_item = self.table.item(row, id_col)
                if id_item:
                    try:
                        problem_id = int(id_item.text())
                    except ValueError:
                        pass
        return problem_id

    def _paste_content_to_cell(self, row, col, value, column_title, main_window):
        """
        将内容粘贴到指定单元格，支持QComboBox验证

        参数:
            row: 行索引
            col: 列索引
            value: 要粘贴的值
            column_title: 列标题
            main_window: 主窗口引用

        返回:
            tuple: (是否成功, 原始值)
        """
        # 检查是否为公式列（只读列）
        item = self.table.item(row, col)
        if item:
            from PyQt5.QtCore import Qt
            if not (item.flags() & Qt.ItemIsEditable):
                # 这是一个只读单元格（可能是公式列），跳过粘贴
                self.logger.debug(f"跳过只读单元格的粘贴操作: 行{row}, 列{col} ({column_title})")
                return False, item.text()

        old_value = ""
        cell_widget = self.table.cellWidget(row, col)

        if cell_widget:
            # 🔧 增强QComboBox粘贴支持
            if hasattr(cell_widget, 'currentText') and hasattr(cell_widget, 'findText'):
                # 这是一个QComboBox
                old_value = cell_widget.currentText()
                self.logger.debug(f"QComboBox粘贴操作 ({row}, {col}): '{old_value}' -> '{value}'")

                if old_value != value:
                    # 🔧 增强功能：支持可编辑QComboBox的直接文本设置
                    success = self._paste_to_combobox(cell_widget, value, column_title, main_window)
                    if success:
                        return True, old_value
                    else:
                        return False, old_value
                else:
                    # 值相同，不需要更改
                    return False, old_value

            # 处理其他控件类型
            elif hasattr(cell_widget, 'text'):
                # QLineEdit等文本控件
                old_value = cell_widget.text()
                if old_value != value:
                    cell_widget.setText(value)
                    self.logger.debug(f"文本控件粘贴成功 ({row}, {col}): '{old_value}' -> '{value}'")
                    return True, old_value

            elif hasattr(cell_widget, 'toPlainText'):
                # QTextEdit等多行文本控件
                old_value = cell_widget.toPlainText()
                if old_value != value:
                    cell_widget.setPlainText(value)
                    self.logger.debug(f"多行文本控件粘贴成功 ({row}, {col}): '{old_value}' -> '{value}'")
                    return True, old_value

            elif hasattr(cell_widget, 'value'):
                # QSpinBox等数值控件
                old_value = str(cell_widget.value())
                try:
                    new_numeric_value = float(value) if value else 0
                    if old_value != str(new_numeric_value):
                        cell_widget.setValue(new_numeric_value)
                        self.logger.debug(f"数值控件粘贴成功 ({row}, {col}): '{old_value}' -> '{new_numeric_value}'")
                        return True, old_value
                except ValueError:
                    self.logger.warning(f"数值控件粘贴失败 ({row}, {col}): 无法转换 '{value}' 为数值")
                    return False, old_value
        else:
            # 对于QTableWidgetItem直接设置文本
            item = self.table.item(row, col)
            if item:
                old_value = item.text()
                if old_value != value:
                    item.setText(value)
                    return True, old_value
            else:
                new_item = QTableWidgetItem(value)
                self.table.setItem(row, col, new_item)
                return True, ""

        return False, old_value

    def _paste_to_combobox(self, combo_widget, value, column_title, main_window):
        """
        将内容粘贴到QComboBox控件

        参数:
            combo_widget: QComboBox控件
            value: 要粘贴的值
            column_title: 列标题
            main_window: 主窗口引用

        返回:
            bool: 是否成功
        """
        try:
            # 🔧 增强功能：支持空值粘贴
            if not value or not value.strip():
                # 空值处理：设置为第一个选项（通常是空选项）
                if combo_widget.count() > 0:
                    combo_widget.setCurrentIndex(0)
                    self.logger.debug(f"QComboBox设置为空值（索引0）")
                    return True
                else:
                    self.logger.warning(f"QComboBox没有选项，无法设置空值")
                    return False

            # 🔧 增强功能：首先尝试精确匹配
            exact_index = combo_widget.findText(value)
            if exact_index >= 0:
                combo_widget.setCurrentIndex(exact_index)
                self.logger.debug(f"QComboBox精确匹配成功，设置索引: {exact_index}")
                return True

            # 🔧 增强功能：如果QComboBox是可编辑的，直接设置文本
            if combo_widget.isEditable():
                # 对于可编辑的QComboBox，可以设置任意文本
                combo_widget.setCurrentText(value)
                self.logger.debug(f"可编辑QComboBox直接设置文本: '{value}'")
                return True

            # 🔧 增强功能：尝试部分匹配（忽略大小写）
            for i in range(combo_widget.count()):
                item_text = combo_widget.itemText(i)
                if item_text.lower() == value.lower():
                    combo_widget.setCurrentIndex(i)
                    self.logger.debug(f"QComboBox忽略大小写匹配成功，设置索引: {i}")
                    return True

            # 🔧 增强功能：如果都不匹配，提供用户友好的错误处理
            self.logger.warning(f"QComboBox粘贴失败: 值 '{value}' 不在选项列表中")

            # 显示详细的错误信息（可选）
            if main_window:
                available_options = [combo_widget.itemText(i) for i in range(combo_widget.count())]
                self._show_combo_paste_error(value, column_title, available_options, main_window)

            return False

        except Exception as e:
            self.logger.error(f"QComboBox粘贴操作异常: {e}")
            return False

    def _show_combo_paste_error(self, value, column_title, available_options, main_window):
        """
        显示QComboBox粘贴错误信息

        参数:
            value: 尝试粘贴的值
            column_title: 列标题
            available_options: 可用选项列表
            main_window: 主窗口引用
        """
        try:
            from PyQt5.QtWidgets import QMessageBox

            # 构建友好的错误信息
            options_preview = available_options[:5] if available_options else []
            options_text = "、".join(f"'{opt}'" for opt in options_preview)

            if len(available_options) > 5:
                options_text += f" 等{len(available_options)}个选项"

            msg = f"无法粘贴到列 '{column_title}'：\n\n"
            msg += f"粘贴内容：'{value}'\n"
            msg += f"该内容不在下拉框的选项列表中。\n\n"

            if options_text:
                msg += f"可用选项包括：{options_text}\n\n"

            msg += "请确保粘贴的内容与下拉框中的某个选项完全匹配。"

            # 使用警告对话框，但不阻塞操作
            QMessageBox.warning(main_window, "粘贴内容无效", msg)

        except Exception as e:
            self.logger.error(f"显示QComboBox错误信息失败: {e}")



    def _validate_combo_paste_value(self, combo_widget, value, column_title, main_window):
        """
        验证QComboBox粘贴值的有效性

        参数:
            combo_widget: QComboBox控件
            value: 要粘贴的值
            column_title: 列标题
            main_window: 主窗口引用

        返回:
            bool: 是否有效
        """
        if not value or not value.strip():
            return True  # 空值总是有效的

        # 获取所有可用选项
        available_options = []
        for i in range(combo_widget.count()):
            available_options.append(combo_widget.itemText(i))

        # 检查值是否在选项列表中
        if value in available_options:
            return True

        # 🔧 功能2：QComboBox复制粘贴功能 - 提供用户友好的提示信息
        self.logger.warning(f"QComboBox粘贴验证失败: 列'{column_title}', 值'{value}' 不在选项列表中")

        # 显示用户友好的提示信息
        if main_window:
            self._show_combo_paste_error(value, column_title, available_options, main_window)

        return False

    def delete_selected_cells(self):
        """
        删除选中单元格的内容（优化版本，减少卡顿）
        """
        selected_cells = self.get_selected_cells()
        if not selected_cells:
            return

        self.logger.info(f"开始删除 {len(selected_cells)} 个选中单元格")

        # 🔧 性能优化：在删除开始时就禁用表格信号，避免每次删除都触发数据库更新
        old_signals_blocked = self.table.blockSignals(True)

        try:
            # 获取主窗口引用
            main_window = None
            widget = self.table
            while widget:
                if hasattr(widget, 'problem_table') and widget.problem_table == self.table:
                    main_window = widget
                    break
                widget = widget.parent()

            # 记录删除操作，用于撤销和数据库更新
            delete_operations = []
            modified_cells = []

            # 执行删除操作
            for row, col in selected_cells:
                # 检查目标单元格是否为特殊处理列(如ID列、复选框列等)
                header_item = self.table.horizontalHeaderItem(col)
                if header_item and header_item.text() == "id":
                    continue  # 跳过ID列

                if col == 0:  # 假设第一列是复选框列
                    continue

                # 获取列标题
                column_title = header_item.text() if header_item else f"列 {col}"

                # 获取问题ID
                problem_id = None
                if main_window:
                    id_col = -1
                    for c in range(self.table.columnCount()):
                        header = self.table.horizontalHeaderItem(c)
                        if header and header.text() == "id":
                            id_col = c
                            break

                    if id_col != -1:
                        id_item = self.table.item(row, id_col)
                        if id_item:
                            try:
                                problem_id = int(id_item.text())
                            except ValueError:
                                pass

                # 获取单元格类型和旧值
                old_value = ""
                cell_widget = self.table.cellWidget(row, col)
                if cell_widget:
                    # 🔧 增强QComboBox删除功能
                    try:
                        if hasattr(cell_widget, 'currentText') and hasattr(cell_widget, 'findText'):
                            # 这是一个QComboBox
                            old_value = cell_widget.currentText()
                            success = self._clear_combobox(cell_widget, row, col)
                            if success:
                                self.logger.debug(f"QComboBox清空成功 ({row}, {col}): '{old_value}' -> 空值")
                            else:
                                self.logger.warning(f"QComboBox清空失败 ({row}, {col})")

                        elif hasattr(cell_widget, 'text'):
                            # QLineEdit等文本控件
                            old_value = cell_widget.text()
                            cell_widget.setText("")
                            self.logger.debug(f"文本控件清空成功 ({row}, {col}): '{old_value}' -> 空值")

                        elif hasattr(cell_widget, 'toPlainText'):
                            # QTextEdit等多行文本控件
                            old_value = cell_widget.toPlainText()
                            cell_widget.setPlainText("")
                            self.logger.debug(f"多行文本控件清空成功 ({row}, {col}): '{old_value}' -> 空值")

                        elif hasattr(cell_widget, 'value'):
                            # QSpinBox等数值控件
                            old_value = str(cell_widget.value())
                            cell_widget.setValue(0)
                            self.logger.debug(f"数值控件清空成功 ({row}, {col}): '{old_value}' -> 0")

                        if old_value:  # 只有当原值不为空时才记录
                            modified_cells.append((row, col))
                            delete_operations.append({
                                'row': row,
                                'col': col,
                                'old_value': old_value,
                            'new_value': "",
                            'problem_id': problem_id,
                            'column_title': column_title
                            })

                    except Exception as e:
                        self.logger.error(f"清空单元格部件值失败 ({row}, {col}): {e}")
                else:
                    # 对于QTableWidgetItem直接清空文本
                    item = self.table.item(row, col)
                    if item:
                        old_value = item.text()
                        if old_value:
                            item.setText("")
                            modified_cells.append((row, col))
                            delete_operations.append({
                                'row': row,
                                'col': col,
                                'old_value': old_value,
                                'new_value': "",
                                'problem_id': problem_id,
                                'column_title': column_title
                            })

            # 🔧 性能优化：使用批量处理删除操作，避免系统卡顿
            if delete_operations:
                self._finalize_delete_operations(delete_operations, modified_cells, main_window)
                self.logger.info(f"批量删除完成: {len(delete_operations)} 个单元格已清空")

        finally:
            # 🔧 确保恢复表格信号状态
            self.table.blockSignals(old_signals_blocked)

    def _finalize_delete_operations(self, delete_operations, modified_cells, main_window):
        """
        完成删除操作的后续处理（优化版本，减少卡顿）

        参数:
            delete_operations: 删除操作列表
            modified_cells: 修改的单元格列表
            main_window: 主窗口引用
        """
        try:
            self.logger.info(f"开始批量处理 {len(delete_operations)} 个删除操作")

            # 🔧 性能优化：检测表格类型并设置相应的批量更新标志
            table_widget = None
            widget = self.table.parent()
            while widget:
                if hasattr(widget, '_updating_display'):
                    table_widget = widget
                    break
                widget = widget.parent()

            # 🔧 关键优化：设置批量更新标志，防止单个单元格更新触发数据库操作
            if table_widget:
                old_updating_display = getattr(table_widget, '_updating_display', False)
                table_widget._updating_display = True
                self.logger.debug(f"已设置表格批量更新标志: {type(table_widget).__name__}")

            try:
                # 🔧 性能优化：批量更新数据库，按记录ID分组
                if main_window and hasattr(main_window, 'update_problem_record'):
                    # 按问题ID分组操作，减少数据库调用次数
                    problem_updates = {}
                    for operation in delete_operations:
                        problem_id = operation['problem_id']
                        column_title = operation['column_title']

                        if problem_id not in problem_updates:
                            problem_updates[problem_id] = {}
                        problem_updates[problem_id][column_title] = ""  # 删除就是设为空值

                    # 批量更新每个问题的多个字段
                    for problem_id, updates in problem_updates.items():
                        if hasattr(main_window, 'batch_update_problem_record'):
                            # 如果支持批量更新，使用批量更新
                            main_window.batch_update_problem_record(problem_id, updates)
                        else:
                            # 否则逐个更新
                            for column_title, new_value in updates.items():
                                main_window.update_problem_record(problem_id, column_title, new_value)

                    self.logger.info(f"批量删除更新了 {len(problem_updates)} 个问题记录")

            finally:
                # 🔧 确保恢复批量更新标志
                if table_widget:
                    table_widget._updating_display = old_updating_display
                    self.logger.debug(f"已恢复表格批量更新标志: {type(table_widget).__name__}")

            # 🔧 性能优化：批量触发表格更新事件
            if modified_cells:
                # 只触发一次整体更新，而不是每个单元格都更新
                self.table.viewport().update()

                # 记录撤销操作（如果支持）
                if main_window and hasattr(main_window, 'record_batch_delete'):
                    main_window.record_batch_delete(delete_operations)

            # 记录日志
            self.logger.info(f"批量删除完成: {len(modified_cells)} 个单元格已清空")

        except Exception as e:
            self.logger.error(f"完成删除操作失败: {e}")

    def _install_combobox_selection_fix(self):
        """
        安装QComboBox选择行为修复器

        解决QComboBox单击直接进入编辑模式，无法进行批量选择的问题
        """
        try:
            combo_count = 0
            # 遍历表格中的所有QComboBox控件
            for row in range(self.table.rowCount()):
                for col in range(self.table.columnCount()):
                    widget = self.table.cellWidget(row, col)
                    if widget and hasattr(widget, 'currentText') and hasattr(widget, 'findText'):
                        # 这是一个QComboBox，为其安装事件过滤器
                        widget.installEventFilter(self)
                        combo_count += 1
                        self.logger.info(f"🔧 为QComboBox ({row}, {col}) 安装事件过滤器，类型: {type(widget).__name__}")

                        # 🔧 **强化调试**：验证事件过滤器是否真的安装成功
                        # 通过检查widget的事件过滤器列表来确认
                        try:
                            # 获取widget的所有事件过滤器（这是一个内部方法，可能不稳定）
                            if hasattr(widget, 'eventFilters'):
                                filters = widget.eventFilters()
                                self.logger.debug(f"🔧 QComboBox ({row}, {col}) 当前事件过滤器数量: {len(filters) if filters else 0}")
                        except:
                            pass

            self.logger.info(f"🔧 QComboBox选择行为修复器安装完成，共处理{combo_count}个QComboBox")

        except Exception as e:
            self.logger.error(f"🔧 安装QComboBox选择行为修复器失败: {e}")
            import traceback
            self.logger.error(f"🔧 详细错误信息: {traceback.format_exc()}")

    def reinstall_combobox_filters(self):
        """
        重新安装QComboBox事件过滤器

        当表格内容发生变化时调用此方法，确保新添加的QComboBox也能正确处理选择事件
        """
        try:
            self.logger.info("🔧 重新安装QComboBox事件过滤器")
            self._install_combobox_selection_fix()
        except Exception as e:
            self.logger.error(f"🔧 重新安装QComboBox事件过滤器失败: {e}")

    def force_reinstall_all_combobox_filters(self):
        """
        强制重新安装所有QComboBox事件过滤器

        这个方法会先移除所有现有的事件过滤器，然后重新安装
        """
        try:
            self.logger.info("🔧 强制重新安装所有QComboBox事件过滤器")

            # 1. 先移除所有现有的事件过滤器
            removed_count = 0
            for row in range(self.table.rowCount()):
                for col in range(self.table.columnCount()):
                    widget = self.table.cellWidget(row, col)
                    if widget and hasattr(widget, 'currentText') and hasattr(widget, 'findText'):
                        # 移除现有的事件过滤器
                        widget.removeEventFilter(self)
                        removed_count += 1
                        self.logger.debug(f"🔧 移除QComboBox ({row}, {col}) 的事件过滤器")

            self.logger.info(f"🔧 已移除{removed_count}个QComboBox的事件过滤器")

            # 2. 重新安装事件过滤器
            self._install_combobox_selection_fix()

            self.logger.info("🔧 强制重新安装QComboBox事件过滤器完成")

        except Exception as e:
            self.logger.error(f"🔧 强制重新安装QComboBox事件过滤器失败: {e}")
            import traceback
            self.logger.error(f"🔧 详细错误信息: {traceback.format_exc()}")

    def test_combobox_event_filter(self):
        """
        测试QComboBox事件过滤器是否工作
        """
        try:
            self.logger.info("🔧 开始测试QComboBox事件过滤器")

            combo_count = 0
            for row in range(self.table.rowCount()):
                for col in range(self.table.columnCount()):
                    widget = self.table.cellWidget(row, col)
                    if widget and hasattr(widget, 'currentText') and hasattr(widget, 'findText'):
                        combo_count += 1
                        self.logger.info(f"🔧 找到QComboBox ({row}, {col})，类型: {type(widget).__name__}")

                        # 尝试手动触发一个测试事件
                        from PyQt5.QtCore import QEvent
                        from PyQt5.QtGui import QMouseEvent
                        from PyQt5.QtCore import Qt, QPoint

                        # 创建一个测试鼠标事件
                        test_event = QMouseEvent(
                            QEvent.MouseButtonPress,
                            QPoint(10, 10),
                            Qt.LeftButton,
                            Qt.LeftButton,
                            Qt.NoModifier
                        )

                        # 测试事件过滤器是否会被调用
                        self.logger.info(f"🔧 测试QComboBox ({row}, {col}) 事件过滤器...")
                        result = self.eventFilter(widget, test_event)
                        self.logger.info(f"🔧 QComboBox ({row}, {col}) 事件过滤器测试结果: {result}")

                        # 只测试第一个QComboBox
                        break
                if combo_count > 0:
                    break

            if combo_count == 0:
                self.logger.warning("🔧 没有找到任何QComboBox控件")

        except Exception as e:
            self.logger.error(f"🔧 测试QComboBox事件过滤器失败: {e}")
            import traceback
            self.logger.error(f"🔧 详细错误信息: {traceback.format_exc()}")

    def _handle_combobox_event(self, combo_widget, event):
        """
        处理QComboBox控件的特殊事件

        参数:
            combo_widget: QComboBox控件
            event: 事件对象

        返回:
            bool: 是否处理了事件
        """
        try:
            # 🔧 **增强调试**：获取当前键盘修饰键状态并记录详细信息
            modifiers = QApplication.keyboardModifiers()
            ctrl_pressed = bool(modifiers & Qt.ControlModifier)
            shift_pressed = bool(modifiers & Qt.ShiftModifier)

            # 🔧 **调试日志**：记录事件类型和修饰键状态
            event_type_name = {
                QEvent.MouseButtonPress: "MouseButtonPress",
                QEvent.MouseButtonRelease: "MouseButtonRelease",
                QEvent.MouseMove: "MouseMove",
                QEvent.FocusIn: "FocusIn",
                QEvent.FocusOut: "FocusOut"
            }.get(event.type(), f"Event_{event.type()}")

            if event.type() in [QEvent.MouseButtonPress, QEvent.MouseMove]:
                combo_row, combo_col = self._find_widget_position(combo_widget)
                self.logger.debug(f"🔧 QComboBox事件: {event_type_name} 位置=({combo_row},{combo_col}) "
                                f"Ctrl={ctrl_pressed} Shift={shift_pressed}")

            # 🔧 处理鼠标按下事件（包括普通点击、Ctrl+点击 和 Shift+点击）
            if event.type() == QEvent.MouseButtonPress:
                combo_row, combo_col = self._find_widget_position(combo_widget)
                if combo_row >= 0 and combo_col >= 0:

                    # 🔧 **调试日志**：记录所有点击事件
                    self.logger.info(f"🔧 QComboBox点击事件: 位置=({combo_row},{combo_col}) "
                                   f"Ctrl={ctrl_pressed} Shift={shift_pressed}")

                    # 🔧 **关键修复**：对于所有点击事件都进行处理，不仅仅是修饰键点击
                    # 这样可以确保QComboBox的选择行为与普通单元格一致

                    # **立即阻止事件传播**
                    event.accept()

                    # 🔧 **激进方法**：完全绕过QComboBox，直接操作表格选择

                    # 1. 强制关闭所有可能的编辑器
                    item = self.table.item(combo_row, combo_col)
                    if item:
                        self.table.closePersistentEditor(item)
                        self.logger.debug(f"🔧 已关闭持久编辑器: ({combo_row},{combo_col})")

                    # 2. 临时完全禁用表格编辑
                    original_triggers = self.table.editTriggers()
                    self.table.setEditTriggers(QAbstractItemView.NoEditTriggers)
                    self.logger.debug(f"🔧 已禁用表格编辑触发器")

                    # 3. 强制QComboBox失去焦点并隐藏下拉框
                    combo_widget.hidePopup()
                    combo_widget.clearFocus()
                    self.logger.debug(f"🔧 已强制QComboBox失去焦点")

                    # 4. 将焦点转移到表格
                    self.table.setFocus()
                    self.logger.debug(f"🔧 已将焦点转移到表格")

                    # 5. 处理选择逻辑（支持普通点击、Ctrl和Shift）
                    self.logger.debug(f"🔧 开始处理选择逻辑: Ctrl={ctrl_pressed} Shift={shift_pressed}")
                    self._handle_combobox_selection(combo_row, combo_col, ctrl_pressed, shift_pressed)

                    # 6. 延迟恢复编辑触发器
                    QTimer.singleShot(100, lambda: self.table.setEditTriggers(original_triggers))
                    self.logger.debug(f"🔧 已设置延迟恢复编辑触发器")

                    self.logger.info(f"🔧 QComboBox点击事件处理完成: ({combo_row}, {combo_col}) "
                                   f"Ctrl={ctrl_pressed}, Shift={shift_pressed}")

                    return True  # 完全阻止事件传播到QComboBox

            # 🔧 处理鼠标拖拽事件（MouseMove + LeftButton）
            elif event.type() == QEvent.MouseMove:
                if event.buttons() & Qt.LeftButton:  # 左键按下时的拖拽
                    # 🔧 支持修饰键的拖拽选择
                    if ctrl_pressed or shift_pressed:
                        combo_row, combo_col = self._find_widget_position(combo_widget)
                        if combo_row >= 0 and combo_col >= 0:

                            self.logger.debug(f"🔧 QComboBox拖拽选择: ({combo_row},{combo_col}) "
                                            f"Ctrl={ctrl_pressed} Shift={shift_pressed}")

                            # **阻止QComboBox的拖拽行为**
                            event.accept()

                            # 强制QComboBox退出编辑状态
                            combo_widget.hidePopup()
                            combo_widget.clearFocus()

                            # 处理拖拽选择逻辑
                            self._handle_combobox_selection(combo_row, combo_col, ctrl_pressed, shift_pressed)

                            return True

            return False  # 允许其他事件正常处理

        except Exception as e:
            self.logger.error(f"处理QComboBox事件失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False

    def _find_widget_position(self, widget):
        """
        查找控件在表格中的位置

        参数:
            widget: 要查找的控件

        返回:
            tuple: (row, col) 位置，如果未找到返回(-1, -1)
        """
        try:
            for row in range(self.table.rowCount()):
                for col in range(self.table.columnCount()):
                    cell_widget = self.table.cellWidget(row, col)
                    if cell_widget == widget:
                        return row, col
            return -1, -1
        except Exception as e:
            self.logger.error(f"查找控件位置失败: {e}")
            return -1, -1

    def _handle_combobox_selection(self, row, col, ctrl_pressed, shift_pressed):
        """
        处理QComboBox的选择逻辑

        参数:
            row: 行索引
            col: 列索引
            ctrl_pressed: 是否按下Ctrl键
            shift_pressed: 是否按下Shift键
        """
        try:
            # 🔧 **调试日志**：记录选择处理开始
            self.logger.info(f"🔧 开始处理QComboBox选择逻辑: 位置=({row},{col}) "
                           f"Ctrl={ctrl_pressed} Shift={shift_pressed}")

            selection_model = self.table.selectionModel()
            if not selection_model:
                self.logger.error("🔧 错误：无法获取选择模型")
                return

            current_index = self.table.model().index(row, col)

            # 🔧 **调试日志**：记录当前选择状态
            current_selected = selection_model.selectedIndexes()
            self.logger.debug(f"🔧 当前已选中单元格数量: {len(current_selected)}")
            self.logger.debug(f"🔧 最后选中单元格: {self.last_selected_cell}")

            # 🔧 **关键修复**：保存当前的last_selected_cell，避免在处理过程中被覆盖
            saved_last_cell = self.last_selected_cell

            if shift_pressed and saved_last_cell:
                # Shift选择：范围选择
                start_row, start_col = saved_last_cell
                self.logger.info(f"🔧 执行Shift范围选择: 从({start_row},{start_col}) 到 ({row},{col})")

                # 🔧 **调试日志**：记录范围选择前的状态
                before_count = len(selection_model.selectedIndexes())
                self.logger.debug(f"🔧 范围选择前已选中: {before_count} 个单元格")

                self._select_range(start_row, start_col, row, col)

                # 🔧 **调试日志**：记录范围选择后的状态
                after_count = len(selection_model.selectedIndexes())
                self.logger.info(f"🔧 Shift范围选择完成: 从{before_count}个增加到{after_count}个单元格")

                # 🔧 **关键修复**：对于Shift选择，不更新last_selected_cell，保持范围选择的起始点
                # 这样连续的Shift选择都会从同一个起始点开始
                self.logger.debug(f"🔧 Shift选择保持起始点: {saved_last_cell}")

            elif shift_pressed and not saved_last_cell:
                # 🔧 **调试日志**：记录Shift选择但没有起始点的情况
                self.logger.warning(f"🔧 Shift选择警告: 没有起始单元格，将作为普通选择处理")
                selection_model.select(current_index, QItemSelectionModel.ClearAndSelect)
                self.table.setCurrentCell(row, col)
                # 设置新的起始点
                self.last_selected_cell = (row, col)
                self.logger.debug(f"🔧 设置新的起始点: {self.last_selected_cell}")

            elif ctrl_pressed:
                # 🔧 **终极修复**：Ctrl多选模式，彻底防止编辑模式
                self.logger.info(f"🔧 执行Ctrl多选: 位置=({row},{col})")

                # 1. **强制确保表格退出编辑状态**
                self.table.clearFocus()
                self.table.setFocus()
                self.logger.debug(f"🔧 已强制表格获得焦点")

                # 2. **立即处理当前单元格的QComboBox**
                combo_widget = self.table.cellWidget(row, col)
                if combo_widget and isinstance(combo_widget, QComboBox):
                    # 强制关闭下拉框和清除焦点
                    combo_widget.hidePopup()
                    combo_widget.clearFocus()
                    self.logger.debug(f"🔧 已强制QComboBox失去焦点")
                    # 如果是可编辑的QComboBox，清除其内部编辑器状态
                    if combo_widget.isEditable() and combo_widget.lineEdit():
                        combo_widget.lineEdit().clearFocus()
                        combo_widget.lineEdit().deselect()
                        self.logger.debug(f"🔧 已清除可编辑QComboBox内部编辑器状态")

                # 3. **处理选择状态**
                is_currently_selected = selection_model.isSelected(current_index)
                self.logger.debug(f"🔧 当前单元格选择状态: {is_currently_selected}")

                if is_currently_selected:
                    # 如果已选中，则取消选择（但保持其他选择）
                    selection_model.select(current_index, QItemSelectionModel.Deselect)
                    self.logger.info(f"🔧 QComboBox取消选择: ({row},{col})")
                else:
                    # 如果未选中，则添加到选择中（保持其他选择）
                    selection_model.select(current_index, QItemSelectionModel.Select)
                    self.logger.info(f"🔧 QComboBox添加选择: ({row},{col})")

                # 4. **设置当前单元格但不触发编辑**
                # 使用NoUpdate标志避免触发编辑模式
                selection_model.setCurrentIndex(current_index, QItemSelectionModel.NoUpdate)
                self.logger.debug(f"🔧 已设置当前索引但不触发更新")

            else:
                # 普通选择：清除其他选择，只选择当前单元格
                self.logger.info(f"🔧 执行普通选择: ({row},{col})")
                selection_model.select(current_index, QItemSelectionModel.ClearAndSelect)
                self.table.setCurrentCell(row, col)
                # 更新最后选中的单元格（普通选择）
                old_last_cell = self.last_selected_cell
                self.last_selected_cell = (row, col)
                self.logger.debug(f"🔧 普通选择更新最后选中单元格: {old_last_cell} -> {self.last_selected_cell}")

            # 🔧 **关键修复**：只有在非Shift选择时才更新last_selected_cell
            # Shift选择应该保持原来的起始点，Ctrl选择需要更新为当前点击的单元格
            if not shift_pressed:
                if ctrl_pressed or not saved_last_cell:  # Ctrl选择或者没有之前的选择
                    old_last_cell = self.last_selected_cell
                    self.last_selected_cell = (row, col)
                    self.logger.debug(f"🔧 更新最后选中单元格: {old_last_cell} -> {self.last_selected_cell}")
            else:
                # Shift选择时保持原来的起始点不变
                self.logger.debug(f"🔧 Shift选择保持起始点不变: {self.last_selected_cell}")

            # 🔧 **终极修复**：确保选择状态立即生效且完全退出编辑模式

            # 1. **强制所有QComboBox退出编辑状态**
            if ctrl_pressed:
                selected_indexes = selection_model.selectedIndexes()
                for index in selected_indexes:
                    widget = self.table.cellWidget(index.row(), index.column())
                    if widget and isinstance(widget, QComboBox):
                        # 强制关闭下拉框和清除焦点
                        widget.hidePopup()
                        widget.clearFocus()
                        # 如果是可编辑QComboBox，清除内部编辑器
                        if widget.isEditable() and widget.lineEdit():
                            widget.lineEdit().clearFocus()
                            widget.lineEdit().deselect()
                    # 强制更新每个选中单元格的显示状态
                    self.table.update(index)

            # 2. **强制表格完全退出编辑状态**
            # 关闭所有可能的持久编辑器
            for index in selection_model.selectedIndexes():
                item = self.table.item(index.row(), index.column())
                if item:
                    self.table.closePersistentEditor(item)

            # 3. **确保表格获得焦点，而不是任何QComboBox**
            self.table.clearFocus()
            self.table.setFocus()

            # 4. **强制刷新视图**
            self.table.viewport().update()

            # 5. **延迟触发选择变化事件和最终检查**
            def final_check():
                self.on_selection_changed()
                # 最终检查：确保没有QComboBox处于编辑状态
                for index in selection_model.selectedIndexes():
                    widget = self.table.cellWidget(index.row(), index.column())
                    if widget and isinstance(widget, QComboBox):
                        widget.hidePopup()
                        widget.clearFocus()

            QTimer.singleShot(50, final_check)

            # 🔧 调试信息
            selected_count = len(selection_model.selectedIndexes()) if selection_model else 0
            self.logger.debug(f"🔧 终极修复完成：选中{selected_count}个单元格，已强制退出编辑模式")

        except Exception as e:
            self.logger.error(f"处理QComboBox选择失败: {e}")

    def _select_range(self, start_row, start_col, end_row, end_col):
        """
        选择指定范围的单元格

        参数:
            start_row: 起始行
            start_col: 起始列
            end_row: 结束行
            end_col: 结束列
        """
        try:
            # 🔧 **调试日志**：记录范围选择开始
            self.logger.info(f"🔧 开始范围选择: 从({start_row},{start_col}) 到 ({end_row},{end_col})")

            # 确保范围正确
            min_row = min(start_row, end_row)
            max_row = max(start_row, end_row)
            min_col = min(start_col, end_col)
            max_col = max(start_col, end_col)

            # 🔧 **调试日志**：记录计算后的范围
            total_cells = (max_row - min_row + 1) * (max_col - min_col + 1)
            self.logger.info(f"🔧 计算范围: 行{min_row}-{max_row}, 列{min_col}-{max_col}, 共{total_cells}个单元格")

            selection_model = self.table.selectionModel()
            if not selection_model:
                self.logger.error("🔧 错误：无法获取选择模型进行范围选择")
                return

            # 🔧 **调试日志**：记录选择前状态
            before_count = len(selection_model.selectedIndexes())
            self.logger.debug(f"🔧 范围选择前已选中: {before_count} 个单元格")

            # 🔧 修复：使用QItemSelection进行批量范围选择，更高效且更可靠
            from PyQt5.QtCore import QItemSelection

            # 创建选择范围
            top_left = self.table.model().index(min_row, min_col)
            bottom_right = self.table.model().index(max_row, max_col)

            # 🔧 **调试日志**：验证索引有效性
            if not top_left.isValid() or not bottom_right.isValid():
                self.logger.error(f"🔧 错误：无效的索引 top_left={top_left.isValid()} bottom_right={bottom_right.isValid()}")
                return

            # 清除当前选择并选择新范围
            selection = QItemSelection(top_left, bottom_right)
            selection_model.select(selection, QItemSelectionModel.ClearAndSelect)

            # 🔧 **调试日志**：记录选择后状态
            after_count = len(selection_model.selectedIndexes())
            self.logger.info(f"🔧 范围选择完成: 从{before_count}个变为{after_count}个单元格")

            # 🔧 **调试日志**：验证选择结果
            if after_count != total_cells:
                self.logger.warning(f"🔧 警告：预期选中{total_cells}个单元格，实际选中{after_count}个")

            # 🔧 **强制刷新视图**
            self.table.viewport().update()
            self.logger.debug(f"🔧 已强制刷新表格视图")

        except Exception as e:
            self.logger.error(f"🔧 范围选择失败: {e}")
            import traceback
            self.logger.error(f"🔧 详细错误信息: {traceback.format_exc()}")

    def _clear_combobox(self, combo_widget, row, col):
        """
        清空QComboBox控件的选择

        参数:
            combo_widget: QComboBox控件
            row: 行索引
            col: 列索引

        返回:
            bool: 是否成功
        """
        try:
            # 🔧 增强功能：智能清空QComboBox
            if combo_widget.count() > 0:
                # 方法1：设置为第一个选项（通常是空选项）
                first_item_text = combo_widget.itemText(0)
                if not first_item_text or first_item_text.strip() == "":
                    # 第一个选项是空选项，设置为它
                    combo_widget.setCurrentIndex(0)
                    self.logger.debug(f"QComboBox设置为空选项（索引0）")
                    return True

                # 方法2：查找空选项
                for i in range(combo_widget.count()):
                    item_text = combo_widget.itemText(i)
                    if not item_text or item_text.strip() == "":
                        combo_widget.setCurrentIndex(i)
                        self.logger.debug(f"QComboBox设置为空选项（索引{i}）")
                        return True

                # 方法3：如果QComboBox是可编辑的，直接设置为空文本
                if combo_widget.isEditable():
                    combo_widget.setCurrentText("")
                    self.logger.debug(f"可编辑QComboBox设置为空文本")
                    return True

                # 方法4：如果没有空选项且不可编辑，设置为第一个选项
                combo_widget.setCurrentIndex(0)
                self.logger.debug(f"QComboBox设置为第一个选项（索引0）作为默认清空")
                return True
            else:
                # QComboBox没有选项
                self.logger.warning(f"QComboBox ({row}, {col}) 没有选项，无法清空")
                return False

        except Exception as e:
            self.logger.error(f"清空QComboBox失败 ({row}, {col}): {e}")
            return False




    def _validate_clipboard_data(self, clipboard_data):
        """
        验证剪贴板数据的有效性

        参数:
            clipboard_data: 要验证的剪贴板数据

        返回:
            bool: 数据是否有效
        """
        if not clipboard_data:
            self.logger.warning("剪贴板数据为空")
            return False

        # 检查是否有有效的行数据
        valid_rows = [row for row in clipboard_data if row and any(str(cell).strip() for cell in row)]
        if not valid_rows:
            self.logger.warning("剪贴板数据不包含有效内容")
            return False

        # 记录数据信息
        max_cols = max(len(row) for row in valid_rows)
        self.logger.info(f"剪贴板数据验证通过: {len(valid_rows)}行 x {max_cols}列")

        return True

    def _handle_paste_size_conflict(self, clipboard_data, start_row, start_col, clipboard_rows, clipboard_cols):
        """
        处理粘贴大小冲突

        参数:
            clipboard_data: 剪贴板数据
            start_row: 起始行
            start_col: 起始列
            clipboard_rows: 剪贴板行数
            clipboard_cols: 剪贴板列数

        返回:
            list or None: 处理后的剪贴板数据，如果取消则返回None
        """
        table_rows = self.table.rowCount()
        table_cols = self.table.columnCount()

        # 计算超出的范围
        row_overflow = max(0, start_row + clipboard_rows - table_rows)
        col_overflow = max(0, start_col + clipboard_cols - table_cols)

        if row_overflow > 0 or col_overflow > 0:
            # 构建提示信息
            message = "粘贴数据超出表格范围：\n"
            if row_overflow > 0:
                message += f"• 超出 {row_overflow} 行\n"
            if col_overflow > 0:
                message += f"• 超出 {col_overflow} 列\n"
            message += "\n选择处理方式："

            # 获取主窗口引用
            main_window = self._get_main_window()
            parent = main_window if main_window else None

            # 创建选择对话框
            msg_box = QMessageBox(parent)
            msg_box.setWindowTitle("粘贴数据大小冲突")
            msg_box.setText(message)
            msg_box.setIcon(QMessageBox.Question)

            # 添加按钮
            truncate_btn = msg_box.addButton("截断数据", QMessageBox.AcceptRole)
            msg_box.addButton("取消粘贴", QMessageBox.RejectRole)

            msg_box.setDefaultButton(truncate_btn)
            msg_box.exec_()

            if msg_box.clickedButton() == truncate_btn:
                # 截断数据以适应表格大小
                truncated_data = self._truncate_clipboard_data(clipboard_data, start_row, start_col, table_rows, table_cols)
                self.logger.info("用户选择截断数据继续粘贴")
                return truncated_data
            else:
                self.logger.info("用户取消粘贴操作")
                return None

        return clipboard_data

    def _truncate_clipboard_data(self, clipboard_data, start_row, start_col, table_rows, table_cols):
        """
        截断剪贴板数据以适应表格大小

        参数:
            clipboard_data: 要截断的剪贴板数据
            start_row: 起始行
            start_col: 起始列
            table_rows: 表格总行数
            table_cols: 表格总列数

        返回:
            list: 截断后的数据
        """
        max_rows = table_rows - start_row
        max_cols = table_cols - start_col

        # 创建截断后的数据副本
        truncated_data = []

        # 截断行数
        rows_to_process = min(len(clipboard_data), max_rows)

        for i in range(rows_to_process):
            row = clipboard_data[i]
            # 截断列数
            truncated_row = row[:max_cols] if len(row) > max_cols else row[:]
            truncated_data.append(truncated_row)

        self.logger.info(f"数据已截断为 {len(truncated_data)}行 x {max_cols}列")
        return truncated_data

    def _get_main_window(self):
        """
        获取主窗口引用

        返回:
            主窗口对象或None
        """
        widget = self.table
        while widget:
            if hasattr(widget, 'problem_table') and widget.problem_table == self.table:
                return widget
            widget = widget.parent()
        return None

    def select_all_cells(self):
        """选择所有单元格"""
        self.table.selectAll()