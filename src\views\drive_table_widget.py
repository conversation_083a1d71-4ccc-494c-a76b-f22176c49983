#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
行驶记录表UI组件
基于统一表格基类实现
"""

import os
import json
import logging
from PyQt5.QtWidgets import (
    QTableWidgetItem, QComboBox, QLineEdit, QTextEdit, QDateEdit,
    QMessageBox, QCheckBox, QWidget, QHBoxLayout, QMenu, QAction,
    QAbstractItemView, QSizePolicy
)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QIcon, QColor

# 公式功能相关导入
from src.views.base_table_widget import BaseTableWidget
from src.formula.formula_config_manager import FormulaConfigManager
import pandas as pd

from src.views.base_table_widget import BaseTableWidget
from src.models.drive import Drive

class DriveTableWidget(BaseTableWidget):
    """
    行驶记录表组件

    继承自BaseTableWidget，自动获得列公式计算功能：
    1. 列公式设置和管理
    2. 自动计算和更新
    3. 公式配置保存和加载
    4. 统一的用户交互界面
    """

    def __init__(self, parent=None):
        # 先调用父类初始化，传入表格名称
        super().__init__(table_name="行驶记录表", parent=parent)

        # 启用公式功能，支持用户手动添加公式列
        self.formula_enabled = True

        # 初始化数据模型
        self.drive_model = Drive()
        self.data_model = self.drive_model  # 设置基类的data_model

        # 添加数据加载状态标志
        self._is_loading_data = False

        # 初始化字段配置
        self.field_groups = self._load_field_groups()
        if not self.field_groups:
            self.logger.error("字段分组配置加载失败，无法继续初始化。")
            # 可以在这里决定是否要禁用某些功能或直接返回
            return

        # 加载字段定义配置
        self.field_definitions = self._load_field_definitions()

        # 加载显示设置
        self.display_settings = self._load_display_settings()

        # 设置数据模型
        self.data_model = self.drive_model

        # 初始化选择处理器以支持多选、复制、粘贴和删除功能
        self._init_selection_handlers()

        # 重新连接所有信号到子类的方法
        try:
            self.table.customContextMenuRequested.disconnect()
        except:
            pass  # 如果没有连接则忽略错误

        try:
            self.table.cellChanged.disconnect()
        except:
            pass  # 如果没有连接则忽略错误

        # 连接公式设置信号，用于在设置公式时更新显示设置
        self.formula_set.connect(self._on_formula_set)

        try:
            self.table.itemSelectionChanged.disconnect()
        except:
            pass  # 如果没有连接则忽略错误

        try:
            self.table.cellClicked.disconnect()
        except:
            pass  # 如果没有连接则忽略错误

        # 重新连接必要的信号
        # 注意：由于上面断开了所有信号连接，需要重新连接所有必要的信号
        self.table.cellChanged.connect(self._on_cell_changed)  # 重新连接单元格变化信号
        self.table.customContextMenuRequested.connect(self._show_context_menu)
        self.table.itemSelectionChanged.connect(self._on_selection_changed)
        self.table.cellClicked.connect(self._on_cell_clicked)

        # 加载数据
        self.load_data()
    
    def _load_field_groups(self):
        """加载字段分组配置"""
        field_groups = {
            '单独列字段': [],
            '固定字段': [],
            '可变字段': []
        }
        
        try:
            # 优先使用统一的字段分组配置文件
            unified_path = os.path.join(os.getcwd(), 'config', 'drive_field_groups.json')
            if os.path.exists(unified_path):
                with open(unified_path, 'r', encoding='utf-8') as f:
                    field_groups = json.load(f)
                    self.logger.info(f"从统一配置文件加载字段分组: {field_groups}")
                    return field_groups

            # 如果统一配置文件不存在，使用分散的配置文件
            # 加载单独列字段
            individual_path = os.path.join(os.getcwd(), 'config', '行驶记录表单独列字段.json')
            if os.path.exists(individual_path):
                with open(individual_path, 'r', encoding='utf-8') as f:
                    individual_data = json.load(f)
                    if individual_data and len(individual_data) > 0:
                        field_groups['单独列字段'] = list(individual_data[0].keys())

            # 加载固定字段
            fixed_path = os.path.join(os.getcwd(), 'config', '行驶记录表固定字段.json')
            if os.path.exists(fixed_path):
                with open(fixed_path, 'r', encoding='utf-8') as f:
                    fixed_data = json.load(f)
                    if fixed_data and len(fixed_data) > 0:
                        field_groups['固定字段'] = list(fixed_data[0].keys())

            # 加载可变字段
            variable_path = os.path.join(os.getcwd(), 'config', '行驶记录表可变字段.json')
            if os.path.exists(variable_path):
                with open(variable_path, 'r', encoding='utf-8') as f:
                    variable_data = json.load(f)
                    if variable_data and len(variable_data) > 0:
                        field_groups['可变字段'] = list(variable_data[0].keys())

            self.logger.info(f"加载字段分组配置成功: 单独字段{len(field_groups['单独列字段'])}个, "
                           f"固定字段{len(field_groups['固定字段'])}个, "
                           f"可变字段{len(field_groups['可变字段'])}个")
            
        except Exception as e:
            self.logger.error(f"加载字段分组配置失败: {e}")
        
        return field_groups

    def _load_field_definitions(self):
        """加载字段定义配置"""
        try:
            # 加载字段定义配置文件
            config_path = os.path.join(os.getcwd(), 'config', 'drive_table_field_definitions.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    definitions = json.load(f)
                    self.logger.info(f"成功加载行驶记录表字段定义配置: {config_path}")
                    return definitions
            else:
                self.logger.warning(f"行驶记录表字段定义配置文件不存在: {config_path}")

        except Exception as e:
            self.logger.error(f"加载行驶记录表字段定义配置失败: {e}")

        return {}

    def _load_display_settings(self):
        """加载显示设置"""
        try:
            settings_path = os.path.join("config", "行驶记录表显示设置.json")
            if os.path.exists(settings_path):
                with open(settings_path, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.logger.info("加载行驶记录表显示设置成功")
                    return settings
            else:
                self.logger.info("行驶记录表显示设置文件不存在，使用默认设置")

        except Exception as e:
            self.logger.error(f"加载行驶记录表显示设置失败: {e}")

        # 返回默认设置，使用统一的格式
        all_fields = (self.field_groups.get("单独列字段", []) +
                     self.field_groups.get("固定字段", []) +
                     self.field_groups.get("可变字段", []))

        return {
            "visible_fields": all_fields,
            "field_order": all_fields
        }

    def apply_display_settings(self, settings):
        """应用显示设置"""
        try:
            self.display_settings = settings
            # 重新加载数据以应用新的显示设置
            self.load_data()
            self.logger.info("行驶记录表显示设置已应用")
        except Exception as e:
            self.logger.error(f"应用行驶记录表显示设置失败: {e}")

    def _expand_json_fields(self, df):
        """展开JSON字段"""
        expanded_df = df.copy()

        try:
            # 首先确保所有固定字段和可变字段的列都存在
            # 从配置文件获取所有可能的字段
            all_fixed_fields = self.field_groups.get('固定字段', [])
            all_variable_fields = self.field_groups.get('可变字段', [])

            # 获取需要添加的新字段
            new_fields = []
            for field in all_fixed_fields + all_variable_fields:
                if field not in expanded_df.columns:
                    new_fields.append(field)

            # 如果有新字段需要添加，按照正确的顺序插入
            if new_fields:
                # 获取显示设置中的字段顺序
                field_order = self.display_settings.get('field_order', [])

                # 如果没有字段顺序配置，使用默认顺序
                if not field_order:
                    # 构建默认顺序：单独列字段 + 固定字段 + 可变字段
                    field_order = (self.field_groups.get('单独列字段', []) +
                                 self.field_groups.get('固定字段', []) +
                                 self.field_groups.get('可变字段', []))

                # 为新字段创建空列，并按照正确的顺序重新排列所有列
                for field in new_fields:
                    expanded_df[field] = None

                # 重新排列列顺序，确保按照field_order的顺序
                existing_columns = expanded_df.columns.tolist()
                ordered_columns = []

                # 按照field_order添加存在的列
                for field in field_order:
                    if field in existing_columns:
                        ordered_columns.append(field)

                # 添加不在field_order中但存在于DataFrame中的列
                for col in existing_columns:
                    if col not in ordered_columns:
                        ordered_columns.append(col)

                # 重新排列DataFrame的列
                expanded_df = expanded_df[ordered_columns]

            # 展开固定字段JSON
            if '行驶记录表固定字段' in df.columns:
                for idx, row in df.iterrows():
                    fixed_json = row['行驶记录表固定字段']
                    if fixed_json and fixed_json.strip():
                        try:
                            fixed_data = json.loads(fixed_json)
                            if isinstance(fixed_data, dict):
                                for key, value in fixed_data.items():
                                    if key in all_fixed_fields:  # 只处理配置中定义的字段
                                        expanded_df.at[idx, key] = value
                        except json.JSONDecodeError as e:
                            self.logger.warning(f"解析固定字段JSON失败 (行{idx}): {e}")

            # 展开可变字段JSON
            if '行驶记录表可变字段' in df.columns:
                for idx, row in df.iterrows():
                    variable_json = row['行驶记录表可变字段']
                    if variable_json and variable_json.strip():
                        try:
                            variable_data = json.loads(variable_json)
                            if isinstance(variable_data, dict):
                                for key, value in variable_data.items():
                                    if key in all_variable_fields:  # 只处理配置中定义的字段
                                        expanded_df.at[idx, key] = value
                        except json.JSONDecodeError as e:
                            self.logger.warning(f"解析可变字段JSON失败 (行{idx}): {e}")

            # 移除原始JSON列
            columns_to_drop = ['行驶记录表固定字段', '行驶记录表可变字段']
            expanded_df = expanded_df.drop(columns=[col for col in columns_to_drop if col in expanded_df.columns])

            self.logger.info(f"JSON字段展开完成，固定字段: {all_fixed_fields}, 可变字段: {all_variable_fields}")

        except Exception as e:
            self.logger.error(f"展开JSON字段失败: {e}")

        return expanded_df
    
    def _apply_display_settings(self, df):
        """应用显示设置"""
        try:
            # 兼容两种格式：新格式(visible_fields)和旧格式(field_visibility)
            visible_fields = self.display_settings.get('visible_fields', [])
            field_order = self.display_settings.get('field_order', [])
            field_visibility = self.display_settings.get('field_visibility', {})

            # 如果使用新格式(visible_fields)，转换为field_visibility格式
            if visible_fields and not field_visibility:
                # 所有字段默认不可见
                all_fields = (self.field_groups.get("单独列字段", []) +
                             self.field_groups.get("固定字段", []) +
                             self.field_groups.get("可变字段", []))
                field_visibility = {field: field in visible_fields for field in all_fields}

            # 过滤可见字段
            visible_columns = []
            for field in field_order:
                if field in df.columns and field_visibility.get(field, True):
                    visible_columns.append(field)

            # 添加不在顺序中但存在于数据中的字段
            for col in df.columns:
                if col not in visible_columns and field_visibility.get(col, True):
                    visible_columns.append(col)

            # 重新排序列
            if visible_columns:
                df = df[visible_columns]

        except Exception as e:
            self.logger.error(f"应用显示设置失败: {e}")

        return df
    
    def _populate_table(self, df):
        """填充表格数据"""
        try:
            # 设置表格大小
            self.table.setRowCount(len(df))
            self.table.setColumnCount(len(df.columns))  # 不添加复选框列

            # 设置表头
            headers = list(df.columns)
            self.table.setHorizontalHeaderLabels(headers)
            
            # 填充数据
            for row_idx, (_, row_data) in enumerate(df.iterrows()):
                # 填充数据列
                for col_idx, (col_name, value) in enumerate(row_data.items()):
                    widget_or_item = self._create_cell_widget(col_name, value, row_idx, col_idx)
                    if isinstance(widget_or_item, QWidget):
                        self.table.setCellWidget(row_idx, col_idx, widget_or_item)
                    else:
                        # 如果返回None，创建QTableWidgetItem
                        if widget_or_item is None:
                            from PyQt5.QtWidgets import QTableWidgetItem
                            widget_or_item = QTableWidgetItem(str(value) if value is not None else "")
                        self.table.setItem(row_idx, col_idx, widget_or_item)
            
            # 调整列宽
            self.table.resizeColumnsToContents()
            
        except Exception as e:
            self.logger.error(f"填充表格数据失败: {e}")
    
    def _create_cell_widget(self, field_name, value, row, col):
        """创建单元格控件"""
        # 根据字段定义配置创建不同的控件
        field_def = self.field_definitions.get(field_name, {})
        field_type = field_def.get('type', 'QLineEdit')
        field_options = field_def.get('options', [])

        # 根据字段类型创建控件
        if field_type == 'QComboBox' and field_options:
            # 🔧 统一QComboBox样式：参考试验状态确认表和费用记录表的标准样式
            # 直接创建QComboBox，不使用容器，避免布局异常
            combo = QComboBox()
            valid_options = [opt for opt in field_options if opt and str(opt).strip()]
            # 🔧 关键修复：添加空选项，允许用户清空选择
            combo.addItem("")  # 添加空选项作为第一项
            combo.addItems(valid_options)
            combo.setEditable(True)  # 🔧 关键修复：设置为可编辑，支持复制粘贴功能
            combo.setInsertPolicy(QComboBox.NoInsert)  # 🔧 关键修复：禁止插入新项，保持下拉选项不变

            # 🔧 关键修复：设置QComboBox的大小策略和约束，防止布局异常
            # 使用Expanding策略允许控件在水平方向上扩展和收缩，但保持在单元格内
            combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

            # 🔧 设置最小和最大宽度，确保控件不会过度缩小或扩展
            combo.setMinimumWidth(80)  # 增加最小宽度，确保内容可见
            combo.setMaximumWidth(300)  # 设置最大宽度，防止过度扩展

            # 🔧 设置高度自适应，与普通文本字段保持一致
            # 不设置固定高度或最大高度限制，让QComboBox完全自适应表格行高
            combo.setMinimumHeight(20)  # 设置较小的最小高度，确保基本可见性
            # 移除最大高度限制，让QComboBox能够随行高自适应调节
            combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)  # 垂直方向也使用Expanding策略

            # 🔧 设置统一样式，确保在小宽度下也能正常显示，移除外层容器框
            combo.setStyleSheet("""
                QComboBox {
                    border: 1px solid #ccc;
                    border-radius: 3px;
                    padding: 2px 5px;
                    background-color: white;
                    min-width: 80px;
                }
                QComboBox::drop-down {
                    border: none;
                    width: 20px;
                }
                QComboBox::down-arrow {
                    width: 12px;
                    height: 12px;
                }
            """)

            # 设置当前值
            if value:
                combo.setCurrentText(str(value))
            else:
                combo.setCurrentIndex(-1)  # 🔧 关键修复：空值时设置为无选择状态，允许用户清空

            # 连接信号以处理值变化
            combo.currentTextChanged.connect(lambda text, r=row, c=col: self._on_widget_changed(r, c, text))
            return combo

        elif field_type == 'QTextEdit':
            text_edit = QTextEdit()
            text_edit.setPlainText(str(value) if value else "")
            text_edit.setMaximumHeight(60)

            # 连接信号以处理值变化
            text_edit.textChanged.connect(lambda r=row, c=col: self._on_widget_changed(r, c, text_edit.toPlainText()))
            return text_edit

        elif field_type == 'QDateEdit':
            date_edit = QDateEdit()
            if value:
                try:
                    date = QDate.fromString(str(value), "yyyy-MM-dd")
                    if date.isValid():
                        date_edit.setDate(date)
                except:
                    pass

            # 连接信号以处理值变化
            date_edit.dateChanged.connect(lambda date, r=row, c=col: self._on_widget_changed(r, c, date.toString("yyyy-MM-dd")))
            return date_edit

        else:
            # 默认使用QTableWidgetItem (QLineEdit类型)
            return None  # 返回None表示使用默认的文本单元格
    
    def add_record(self, batch_mode=False):
        """
        添加记录

        参数:
            batch_mode: 是否为批量模式，批量模式下不会立即重新加载表格
        """
        try:
            # 创建默认数据
            default_data = {}

            # 统一使用中文键名，并使用.get()安全访问
            individual_fields = self.field_groups.get('单独列字段', [])
            fixed_fields = self.field_groups.get('固定字段', [])
            variable_fields = self.field_groups.get('可变字段', [])

            # 从配置文件获取单独列字段的默认值
            if individual_fields:
                individual_path = os.path.join(os.getcwd(), 'config', '行驶记录表单独列字段.json')
                if os.path.exists(individual_path):
                    with open(individual_path, 'r', encoding='utf-8') as f:
                        individual_templates = json.load(f)
                        if individual_templates:
                            template_data = individual_templates[0].copy()
                            # 移除id字段，因为它是自增主键
                            if 'id' in template_data:
                                del template_data['id']

                            # 处理数据类型，将空字符串转换为None或适当的默认值
                            for key, value in template_data.items():
                                if value == "":
                                    # 对于数值字段，设置为None
                                    if key in ["试验里程", "泊车次数", "加油量", "充电量", "驾驶员工时", "测试员工时"]:
                                        template_data[key] = None
                                    # 对于日期字段，设置为当前日期
                                    elif key == "日期":
                                        from datetime import datetime
                                        template_data[key] = datetime.now().strftime("%Y-%m-%d")
                                # 🔧 关键修复：对于QComboBox字段，强制设置为空值，确保新增行时下拉框显示为空
                                elif key in ["天气", "试验工况", "UDLC次数"]:
                                    template_data[key] = ""
                            default_data.update(template_data)

            # 添加固定字段的默认值
            if fixed_fields:
                fixed_path = os.path.join(os.getcwd(), 'config', '行驶记录表固定字段.json')
                if os.path.exists(fixed_path):
                    with open(fixed_path, 'r', encoding='utf-8') as f:
                        fixed_templates = json.load(f)
                        if fixed_templates and len(fixed_templates) > 0:
                            # 使用最后一个模板（通常是空模板）
                            fixed_template = fixed_templates[-1]
                            for field in fixed_fields:
                                if field in fixed_template:
                                    default_data[field] = fixed_template[field]

            # 添加可变字段的默认值
            if variable_fields:
                variable_path = os.path.join(os.getcwd(), 'config', '行驶记录表可变字段.json')
                if os.path.exists(variable_path):
                    with open(variable_path, 'r', encoding='utf-8') as f:
                        variable_templates = json.load(f)
                        if variable_templates and len(variable_templates) > 0:
                            # 使用最后一个模板（通常是空模板）
                            variable_template = variable_templates[-1]
                            for field in variable_fields:
                                if field in variable_template:
                                    default_data[field] = variable_template[field]

            # 添加记录到数据库
            # 统一使用中文键名，并使用.get()安全访问
            record_id = self.drive_model.add_record(
                default_data,
                {
                    '单独列字段': self.field_groups.get('单独列字段', []),
                    '固定字段': self.field_groups.get('固定字段', []),
                    '可变字段': self.field_groups.get('可变字段', [])
                }
            )

            if record_id:
                # 性能优化：批量模式下不重新加载表格，由调用方统一处理
                if not batch_mode:
                    # 重新加载数据
                    self.load_data()

                self.record_added.emit(record_id)

                # 减少日志输出频率
                if record_id % 10 == 0:
                    self.logger.info(f"添加行驶记录成功，当前ID: {record_id}")

                return record_id
            else:
                if not batch_mode:
                    QMessageBox.warning(self, "警告", "添加记录失败")
                return None

        except Exception as e:
            self.logger.error(f"添加行驶记录失败: {e}")
            if not batch_mode:
                QMessageBox.warning(self, "错误", f"添加记录失败: {e}")
            return None

    def add_multiple_records(self, count):
        """
        批量添加多条记录

        参数:
            count: 要添加的记录数量

        返回:
            成功添加的记录ID列表
        """
        try:
            self.logger.info(f"开始批量添加 {count} 条行驶记录")

            # 临时禁用表格信号，避免在批量操作期间触发单元格更新事件
            original_signals_blocked = self.table.signalsBlocked()
            self.table.blockSignals(True)

            # 设置批量操作标志
            self._updating_display = True

            added_record_ids = []

            try:
                # 批量添加记录
                for i in range(count):
                    record_id = self.add_record(batch_mode=True)
                    if record_id:
                        added_record_ids.append(record_id)
                    else:
                        self.logger.warning(f"批量添加第 {i+1} 条行驶记录失败")

                # 批量操作完成后，统一重新加载表格
                if added_record_ids:
                    self.load_data()
                    self.logger.info(f"批量添加完成，成功添加 {len(added_record_ids)} 条行驶记录")

                    # 发射批量添加完成信号
                    for record_id in added_record_ids:
                        self.record_added.emit(record_id)

                return added_record_ids

            finally:
                # 恢复表格信号和标志
                self._updating_display = False
                self.table.blockSignals(original_signals_blocked)

        except Exception as e:
            self.logger.error(f"批量添加行驶记录失败: {e}")
            # 确保在异常情况下也恢复信号状态
            self._updating_display = False
            self.table.blockSignals(original_signals_blocked)
            QMessageBox.critical(self, "错误", f"批量添加记录失败: {e}")
            return []

    def delete_record(self):
        """删除记录"""
        try:
            # 获取选中的行
            selected_rows = set()
            for item in self.table.selectedItems():
                selected_rows.add(item.row())
            
            if not selected_rows:
                QMessageBox.information(self, "提示", "请先选择要删除的记录")
                return
            
            # 确认删除
            reply = QMessageBox.question(self, "确认删除", 
                                       f"确定要删除选中的 {len(selected_rows)} 条记录吗？",
                                       QMessageBox.Yes | QMessageBox.No)
            
            if reply == QMessageBox.Yes:
                # 获取记录ID并删除
                for row in sorted(selected_rows, reverse=True):
                    # ID列现在是第一列
                    id_item = self.table.item(row, 0)
                    if id_item:
                        record_id = id_item.text()
                        if record_id.isdigit():
                            self.drive_model.delete_record(int(record_id))
                
                # 重新加载数据
                self.load_data()
                QMessageBox.information(self, "成功", f"已删除 {len(selected_rows)} 条记录")
                
        except Exception as e:
            self.logger.error(f"删除行驶记录失败: {e}")
            QMessageBox.warning(self, "错误", f"删除记录失败: {e}")

    def _show_context_menu(self, position):
        """显示增强的右键菜单，与试验问题表保持一致"""
        menu = QMenu(self)

        # 创建 "新增" 的级联菜单
        add_menu = menu.addMenu("新增")
        add_blank_row_action = add_menu.addAction("新增空白行")

        # 添加行插入功能
        insert_above_action = menu.addAction("向上方插入行")
        insert_below_action = menu.addAction("向下方插入行")

        menu.addSeparator()

        # 复制、粘贴、删除操作
        copy_action = menu.addAction("复制")
        paste_action = menu.addAction("粘贴")
        paste_as_new_action = menu.addAction("粘贴为新行")
        delete_action = menu.addAction("删除")

        menu.addSeparator()

        # 创建 "导出" 的级联菜单
        export_menu = menu.addMenu("导出")
        export_table_action = export_menu.addAction("导出表格")

        # 刷新
        refresh_action = menu.addAction("刷新")

        # 检查是否有选中的单元格或行
        selected_items = self.table.selectedItems()
        has_selection = len(selected_items) > 0

        # 根据选择状态启用/禁用相关操作
        if not has_selection:
            copy_action.setEnabled(False)
            delete_action.setEnabled(False)
        else:
            copy_action.setEnabled(True)
            delete_action.setEnabled(True)

        # 检查是否有复制的数据
        has_copied_data = hasattr(self, 'table_selection_handler') and \
                         hasattr(self.table_selection_handler, 'clipboard_data') and \
                         len(self.table_selection_handler.clipboard_data) > 0

        if not has_copied_data:
            paste_action.setEnabled(False)
            paste_as_new_action.setEnabled(False)
        else:
            paste_action.setEnabled(True)
            paste_as_new_action.setEnabled(True)

        # 显示菜单并获取所选操作
        action = menu.exec_(self.table.mapToGlobal(position))

        # 处理菜单操作
        if action == add_blank_row_action:
            self.add_record()  # 调用正确的add_record方法，会在数据库中创建记录并生成ID
        elif action == insert_above_action:
            # 使用行插入管理器进行多行插入
            self.insert_rows_above()
        elif action == insert_below_action:
            # 使用行插入管理器进行多行插入
            self.insert_rows_below()
        elif action == copy_action:
            self._copy_selection()
        elif action == paste_action:
            self._paste_selection()
        elif action == paste_as_new_action:
            self._paste_as_new_row()
        elif action == delete_action:
            self.delete_record()
        elif action == export_table_action:
            self.export_data()
        elif action == refresh_action:
            self.load_data()

    def insert_rows_above(self):
        """向上方插入行"""
        if hasattr(self, 'row_insert_manager'):
            self.row_insert_manager.insert_rows_above()
        else:
            self.logger.error("行插入管理器未初始化")
            QMessageBox.warning(self, "错误", "行插入功能未就绪")

    def insert_rows_below(self):
        """向下方插入行"""
        if hasattr(self, 'row_insert_manager'):
            self.row_insert_manager.insert_rows_below()
        else:
            self.logger.error("行插入管理器未初始化")
            QMessageBox.warning(self, "错误", "行插入功能未就绪")

    def _insert_row_above(self):
        """向上方插入行 - 实际调用add_record创建新记录"""
        self.add_record()

    def _insert_row_below(self):
        """向下方插入行 - 实际调用add_record创建新记录"""
        self.add_record()

    def _copy_selection(self):
        """复制选中内容"""
        if hasattr(self, 'table_selection_handler'):
            self.table_selection_handler.copy_selected_cells()
        else:
            self.logger.warning("表格选择处理器未初始化")

    def _paste_selection(self):
        """粘贴内容"""
        if hasattr(self, 'table_selection_handler'):
            self.table_selection_handler.paste_to_selected_cells()
        else:
            self.logger.warning("表格选择处理器未初始化")

    def _paste_as_new_row(self):
        """粘贴为新行"""
        # 这个功能需要根据具体需求实现
        QMessageBox.information(self, "提示", "粘贴为新行功能待实现")
        self.logger.info("粘贴为新行功能被调用")
    
    def _on_cell_changed(self, row, column):
        """单元格内容变化处理"""
        try:
            # 防止在更新显示时触发数据保存
            if hasattr(self, '_updating_display') and self._updating_display:
                return

            # 🔧 新增：首先调用父类的撤销重做功能
            super()._on_cell_changed(row, column)

            # 获取字段名
            header_item = self.table.horizontalHeaderItem(column)
            if not header_item:
                return

            field_name = header_item.text()

            # 如果是公式列，不允许直接编辑，跳过保存
            if hasattr(self, 'formula_columns') and field_name in self.formula_columns:
                self.logger.debug(f"跳过公式列 {field_name} 的数据保存")
                return

            # 获取记录ID（现在在第一列）
            id_item = self.table.item(row, 0)
            if not id_item:
                return

            record_id = id_item.text()
            if not record_id.isdigit():
                return

            record_id = int(record_id)

            # 获取新值
            item = self.table.item(row, column)
            widget = self.table.cellWidget(row, column)

            if widget:
                if isinstance(widget, QComboBox):
                    new_value = widget.currentText()
                    self.logger.info(f"📋 下拉框新值: {new_value}")
                elif isinstance(widget, QDateEdit):
                    new_value = widget.date().toString("yyyy-MM-dd")
                    self.logger.info(f"📅 日期新值: {new_value}")
                elif isinstance(widget, QTextEdit):
                    new_value = widget.toPlainText()
                    self.logger.info(f"📝 多行文本框新值: {new_value}")
                elif isinstance(widget, QLineEdit):
                    new_value = widget.text()
                    self.logger.info(f"📝 单行文本框新值: {new_value}")
                else:
                    self.logger.warning(f"❌ 未知控件类型: {type(widget)}")
                    return
            elif item:
                new_value = item.text()
            else:
                return

            # 更新数据库
            update_data = {field_name: new_value}
            success = self.drive_model.update_record(record_id, update_data, self.field_groups)

            if success:
                self.record_updated.emit(record_id)

                # 🔧 改进：先同步单元格数据到数据框，然后触发公式重新计算
                self._sync_single_cell_to_dataframe(row, column, new_value)

                # 🔧 新增：触发公式重新计算
                if (hasattr(self, 'formula_enabled') and self.formula_enabled and
                    hasattr(self, 'formula_columns') and self.formula_columns):
                    self._trigger_formula_recalculation(field_name, row)

            else:
                self.logger.warning(f"更新行驶记录失败，ID: {record_id}")

        except Exception as e:
            self.logger.error(f"处理单元格变化失败: {e}")

    def _save_formula_results_to_database(self, formula_column: str, calculated_series: pd.Series):
        """将公式计算结果保存到数据库 - 优化版本，使用批量更新"""
        try:
            # 🔧 性能优化：收集所有需要更新的数据，进行批量更新
            batch_updates = []

            for row_index, value in calculated_series.items():
                record_id = self._get_record_id_for_row(row_index)
                if record_id:
                    batch_updates.append({
                        'id': record_id,
                        'data': {formula_column: value}
                    })

            if batch_updates:
                # 🔧 使用批量更新方法（如果数据模型支持）
                if hasattr(self.drive_model, 'batch_update_records'):
                    success_count = self.drive_model.batch_update_records(batch_updates, self.field_groups)
                    self.logger.info(f"批量保存公式结果到数据库: {formula_column}列，成功更新{success_count}/{len(batch_updates)}条记录")
                else:
                    # 🔧 降级到逐条更新，但减少日志输出
                    success_count = 0
                    for update_item in batch_updates:
                        success = self.drive_model.update_record(update_item['id'], update_item['data'], self.field_groups)
                        if success:
                            success_count += 1

                    # 🔧 只输出汇总日志，避免大量DEBUG日志
                    self.logger.info(f"保存公式结果到数据库: {formula_column}列，成功更新{success_count}/{len(batch_updates)}条记录")

                    # 🔧 如果有失败的记录，输出警告
                    if success_count < len(batch_updates):
                        self.logger.warning(f"部分公式结果保存失败: {formula_column}列，失败{len(batch_updates) - success_count}条记录")

        except Exception as e:
            self.logger.error(f"保存行驶记录公式结果到数据库失败: {e}")

    def _get_record_id_for_row(self, row_index: int):
        """获取指定行的记录ID"""
        try:
            # 行驶记录表的ID现在在第一列
            id_item = self.table.item(row_index, 0)
            if id_item and id_item.text().isdigit():
                return int(id_item.text())
            return None
        except Exception as e:
            self.logger.error(f"获取行驶记录行{row_index}的ID失败: {e}")
            return None

    def _on_widget_changed(self, row, column, new_value):
        """控件值变化处理"""
        try:
            # 如果正在加载数据，忽略控件信号
            if hasattr(self, '_is_loading_data') and self._is_loading_data:
                return

            # 防止在更新显示时触发数据保存
            if hasattr(self, '_updating_display') and self._updating_display:
                return

            # 获取字段名
            header_item = self.table.horizontalHeaderItem(column)
            if not header_item:
                return

            field_name = header_item.text()

            # 如果是公式列，不允许直接编辑，跳过保存
            if hasattr(self, 'formula_columns') and field_name in self.formula_columns:
                self.logger.debug(f"跳过公式列 {field_name} 的控件值保存")
                return

            # 获取记录ID（现在在第一列）
            id_item = self.table.item(row, 0)
            if not id_item:
                return

            record_id = id_item.text()
            if not record_id.isdigit():
                return

            record_id = int(record_id)

            # 获取字段名
            header_item = self.table.horizontalHeaderItem(column)
            if not header_item:
                return

            field_name = header_item.text()

            # 更新数据库
            update_data = {field_name: new_value}
            success = self.drive_model.update_record(record_id, update_data, self.field_groups)

            if success:
                self.record_updated.emit(record_id)
                self.logger.info(f"控件值变化更新成功，ID: {record_id}, 字段: {field_name}, 新值: {new_value}")

                # 🔧 改进：先同步控件数据到数据框，然后触发公式重新计算
                self._sync_single_cell_to_dataframe(row, column, new_value)

                # 🔧 新增：触发公式重新计算
                if (hasattr(self, 'formula_enabled') and self.formula_enabled and
                    hasattr(self, 'formula_columns') and self.formula_columns):
                    self._trigger_formula_recalculation(field_name, row)

            else:
                self.logger.warning(f"控件值变化更新失败，ID: {record_id}, 字段: {field_name}")

        except Exception as e:
            self.logger.error(f"处理控件值变化失败: {e}")

    def _on_selection_changed(self):
        """选择变化处理"""
        try:
            # 🔧 关键修复：使用selectionModel获取选中行，支持包含QComboBox的行选择
            selection_model = self.table.selectionModel()
            if selection_model:
                selected_indexes = selection_model.selectedRows()
                if selected_indexes:
                    row = selected_indexes[0].row()
                    # 获取记录ID（现在在第一列）
                    id_item = self.table.item(row, 0)
                    if id_item and id_item.text().isdigit():
                        record_id = int(id_item.text())
                        self.record_selected.emit(record_id)
            else:
                # 回退到原有方法
                selected_items = self.table.selectedItems()
                if selected_items:
                    row = selected_items[0].row()
                    # 获取记录ID（现在在第一列）
                    id_item = self.table.item(row, 0)
                    if id_item and id_item.text().isdigit():
                        record_id = int(id_item.text())
                        self.record_selected.emit(record_id)
        except Exception as e:
            self.logger.error(f"处理选择变化失败: {e}")

    def _on_cell_clicked(self, row, column):
        """单元格点击处理"""
        try:
            # 如果当前有单元格正在编辑，先保存编辑内容
            if self.table.state() == QAbstractItemView.EditingState:
                current_item = self.table.currentItem()
                if current_item:
                    # 结束编辑，这会触发cellChanged信号自动保存
                    self.table.closePersistentEditor(current_item)
        except Exception as e:
            self.logger.error(f"处理单元格点击失败: {e}")

    def reload_field_definitions(self):
        """重新加载字段定义配置"""
        try:
            self.field_definitions = self._load_field_definitions()
            self.logger.info("字段定义配置重新加载成功")
        except Exception as e:
            self.logger.error(f"重新加载字段定义配置失败: {e}")

    def import_field_json(self, json_data):
        """导入字段JSON配置"""
        try:
            # 重新加载字段分组配置
            self.field_groups = self._load_field_groups()

            # 重新加载字段定义配置
            self.reload_field_definitions()

            # 重新加载数据以应用新的字段配置
            self.load_data()
            self.logger.info("行驶记录表字段JSON配置导入成功")
        except Exception as e:
            self.logger.error(f"导入行驶记录表字段JSON配置失败: {e}")
            raise


    # ==================== 公式功能相关方法 ====================

    def load_formula_config(self):
        """加载公式配置"""
        try:
            if self.formula_config_manager.load_table_formulas(self.table_name, self.formula_engine):
                # 更新公式列标识
                column_formulas = self.formula_engine.get_column_formulas(self.table_name)
                self.formula_columns = set(column_formulas.keys())
                
                # 更新列头样式
                for column_name in self.formula_columns:
                    self._update_column_header_style(column_name, True)
                
                self.logger.info(f'成功加载行驶记录表公式配置，共{len(self.formula_columns)}个公式列')
            else:
                self.logger.warning(f'加载行驶记录表公式配置失败')
                
        except Exception as e:
            self.logger.error(f'加载行驶记录表公式配置异常: {e}')

    def save_formula_config(self):
        """保存公式配置"""
        try:
            if self.formula_config_manager.save_table_formulas(self.table_name, self.formula_engine):
                self.logger.info(f'成功保存行驶记录表公式配置')
                return True
            else:
                self.logger.error(f'保存行驶记录表公式配置失败')
                return False

        except Exception as e:
            self.logger.error(f'保存行驶记录表公式配置异常: {e}')
            return False

    def _on_formula_set(self, table_name, column_name, formula):
        """处理公式设置信号"""
        try:
            if table_name != self.table_name:
                return

            self.logger.info(f"行驶记录表公式设置: {column_name} = {formula}")

            # 如果设置的是新列的公式，可能需要更新显示设置
            # 这里我们重新加载数据以确保列顺序正确
            self.load_data()

        except Exception as e:
            self.logger.error(f"处理公式设置信号失败: {e}")

    def load_data(self):
        """
        加载数据并应用公式计算
        重写基类方法以支持公式功能
        """
        try:
            # 获取数据 - 使用drive_model
            if hasattr(self, 'drive_model') and hasattr(self.drive_model, 'get_all_records'):
                df = self.drive_model.get_all_records()
            else:
                df = pd.DataFrame()  # 如果没有数据模型，使用空数据框

            if df.empty:
                self.logger.info('没有行驶记录表数据')
                self.data_frame = pd.DataFrame()
                # 清空表格显示
                self.table.setRowCount(0)
                self.table.setColumnCount(0)
                return

            # 先展开JSON字段，确保公式计算能访问到所有字段
            expanded_df = self._expand_json_fields(df)

            # 设置数据框
            self.data_frame = expanded_df.copy()

            # 如果有公式列，重新计算
            if hasattr(self, 'formula_enabled') and self.formula_enabled and hasattr(self, 'formula_columns') and self.formula_columns:
                if hasattr(self, 'formula_engine'):
                    self.data_frame = self.formula_engine.calculate_all_formula_columns(
                        self.table_name, self.data_frame
                    )

            # 设置表格显示 - 使用_setup_table_with_data以应用显示设置
            self._setup_table_with_data(self.data_frame)

            # 加载公式配置
            self.load_formula_config()

            self.logger.info(f'成功加载 {len(df)} 条行驶记录表记录')

        except Exception as e:
            self.logger.error(f'加载行驶记录表数据失败: {e}')
            QMessageBox.warning(self, "错误", f"加载数据失败: {e}")

    def _setup_table_with_data(self, df):
        """设置表格数据显示"""
        try:
            # 展开JSON字段
            df = self._expand_json_fields(df)

            # 应用显示设置
            df = self._apply_display_settings(df)

            # 调用显示方法
            self._setup_table_display_only(df)

        except Exception as e:
            self.logger.error(f"设置行驶记录表显示失败: {e}")

    def _setup_table_display_only(self, df):
        """仅设置表格显示，不进行JSON字段展开"""
        try:
            # 根据显示设置获取可见字段和顺序
            visible_fields = self.display_settings.get("visible_fields", [])
            field_order = self.display_settings.get("field_order", [])

            # 使用字段顺序，只显示可见字段且存在于数据框中的字段
            ordered_visible_fields = [field for field in field_order
                                    if field in visible_fields and field in df.columns]

            # 如果没有有效的字段顺序，使用原始列顺序
            if not ordered_visible_fields:
                ordered_visible_fields = df.columns.tolist()

            # 设置数据加载状态标志，阻止控件信号处理
            self._is_loading_data = True

            # 使用blockSignals阻止表格信号，这比disconnect更可靠
            self.table.blockSignals(True)

            # 不添加复选框列，直接使用字段列
            headers = ordered_visible_fields

            self.table.setColumnCount(len(headers))
            self.table.setHorizontalHeaderLabels(headers)
            self.table.setRowCount(len(df))

            # 填充数据
            for row in range(len(df)):
                # 填充数据列 - 按照ordered_visible_fields的顺序
                for col_idx, field_name in enumerate(ordered_visible_fields):
                    if field_name in df.columns:
                        value = df.loc[df.index[row], field_name]
                        if value is None:
                            value = ""

                        # 检查是否是公式列
                        is_formula_column = hasattr(self, 'formula_columns') and field_name in self.formula_columns

                        if is_formula_column:
                            # 公式列：创建只读的表格项
                            item = QTableWidgetItem(str(value))
                            # 设置为只读
                            item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                            # 设置公式列样式
                            if isinstance(value, str) and value.startswith("#"):
                                # 错误值
                                item.setBackground(QColor("#ffebee"))
                                item.setForeground(QColor("#d32f2f"))
                            else:
                                # 正常公式值
                                item.setBackground(QColor("#f3e5f5"))
                                item.setForeground(QColor("#7b1fa2"))
                            self.table.setItem(row, col_idx, item)
                        else:
                            # 非公式列：使用字段定义创建控件
                            widget = self._create_cell_widget(field_name, value, row, col_idx)

                            if widget is not None:
                                # 使用特殊控件
                                self.table.setCellWidget(row, col_idx, widget)
                            else:
                                # 使用默认的文本单元格，确保可编辑
                                item = QTableWidgetItem(str(value))
                                item.setFlags(item.flags() | Qt.ItemIsEditable)
                                self.table.setItem(row, col_idx, item)

            # 调整列宽
            self.table.resizeColumnsToContents()

            self.logger.info(f"行驶记录表显示设置完成，共 {len(df)} 行 {len(ordered_visible_fields)} 列")

            # 🔧 **关键修复**：行驶记录表显示设置完成后重新安装QComboBox事件过滤器
            # 这确保新创建的QComboBox控件能够正确处理多选事件
            if hasattr(self, 'table_selection_handler') and self.table_selection_handler:
                self.table_selection_handler.force_reinstall_all_combobox_filters()
                self.logger.info(f"🔧 行驶记录表显示完成，已重新安装QComboBox事件过滤器")

        except Exception as e:
            self.logger.error(f"设置行驶记录表显示失败: {e}")
        finally:
            # 确保在任何情况下都重新启用信号和重置加载状态
            self.table.blockSignals(False)
            self._is_loading_data = False

    def _refresh_table_display(self):
        """重写基类方法，支持行驶记录表的特殊控件"""
        try:
            if self.data_frame.empty:
                return

            # 设置更新标志，防止递归
            self._updating_display = True

            # 阻止表格信号，防止触发cellChanged和itemChanged
            self.table.blockSignals(True)

            # 获取当前显示的字段
            current_headers = []
            for col in range(self.table.columnCount()):
                header_item = self.table.horizontalHeaderItem(col)
                if header_item:
                    current_headers.append(header_item.text())

            # 填充数据 - 只更新现有列的数据
            for row in range(min(len(self.data_frame), self.table.rowCount())):
                for col_idx, field_name in enumerate(current_headers):
                    if field_name in self.data_frame.columns:
                        table_col = col_idx  # 直接使用列索引
                        value = self.data_frame.loc[self.data_frame.index[row], field_name]

                        # 检查是否是公式列
                        is_formula_column = hasattr(self, 'formula_columns') and field_name in self.formula_columns

                        # 获取现有的控件或表格项
                        existing_widget = self.table.cellWidget(row, table_col)
                        existing_item = self.table.item(row, table_col)

                        if is_formula_column:
                            # 公式列：移除控件，创建只读表格项
                            if existing_widget:
                                self.table.removeCellWidget(row, table_col)
                                item = QTableWidgetItem()
                                self.table.setItem(row, table_col, item)
                            else:
                                item = existing_item if existing_item else QTableWidgetItem()
                                if not existing_item:
                                    self.table.setItem(row, table_col, item)

                            # 设置值
                            display_value = str(value) if pd.notna(value) else ""
                            item.setText(display_value)

                            # 设置为只读
                            item.setFlags(item.flags() & ~Qt.ItemIsEditable)

                            # 设置公式列样式
                            if isinstance(value, str) and value.startswith("#"):
                                # 错误值
                                item.setBackground(QColor("#ffebee"))
                                item.setForeground(QColor("#d32f2f"))
                            else:
                                # 正常公式值
                                item.setBackground(QColor("#f3e5f5"))
                                item.setForeground(QColor("#7b1fa2"))
                        else:
                            # 非公式列：保持现有控件，只更新值
                            if existing_widget:
                                # 更新控件的值
                                self._update_widget_value(existing_widget, value)
                            else:
                                # 更新表格项的值，确保可编辑
                                item = existing_item if existing_item else QTableWidgetItem()
                                if not existing_item:
                                    self.table.setItem(row, table_col, item)

                                display_value = str(value) if pd.notna(value) else ""
                                item.setText(display_value)

                                # 确保可编辑
                                item.setFlags(item.flags() | Qt.ItemIsEditable)

                                # 恢复默认样式
                                item.setBackground(QColor("#ffffff"))
                                item.setForeground(QColor("#000000"))

            # 更新列头样式
            if hasattr(self, 'formula_columns'):
                for column_name in self.formula_columns:
                    self._update_column_header_style(column_name, True)

            self.logger.info(f"行驶记录表显示刷新完成")

        except Exception as e:
            self.logger.error(f"行驶记录表刷新显示失败: {e}")
        finally:
            # 重新启用表格信号
            self.table.blockSignals(False)
            # 清除更新标志
            self._updating_display = False

    def _update_widget_value(self, widget, value):
        """更新控件的值"""
        try:
            if isinstance(widget, QComboBox):
                widget.setCurrentText(str(value) if value is not None else "")
            elif isinstance(widget, QLineEdit):
                widget.setText(str(value) if value is not None else "")
            elif isinstance(widget, QTextEdit):
                widget.setPlainText(str(value) if value is not None else "")
            elif isinstance(widget, QDateEdit):
                if value:
                    try:
                        date = QDate.fromString(str(value), "yyyy-MM-dd")
                        if date.isValid():
                            widget.setDate(date)
                    except:
                        pass
        except Exception as e:
            self.logger.error(f"更新控件值失败: {e}")




