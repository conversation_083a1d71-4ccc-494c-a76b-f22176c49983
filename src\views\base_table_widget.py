#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
统一表格基类
提供通用的表格功能，包括选中、右键菜单、键盘输入等
现已集成列级别公式计算功能
"""

import os
import json
import logging
import pandas as pd
from typing import Dict, Any, Optional
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QHeaderView, QComboBox, QLineEdit, QTextEdit, QDateEdit, QPushButton,
    QToolBar, QAction, QMenu, QMessageBox, QCheckBox, QFrame, QAbstractItemView,
    QDialog, QFormLayout, QListWidget, QGroupBox, QLabel
)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QEvent
from PyQt5.QtGui import QIcon, QColor

from src.utils.resource_loader import ResourceLoader
from src.formula.column_formula_engine import ColumnFormulaEngine
from src.formula.formula_dialog import ColumnFormulaDialog

class BaseTableWidget(QWidget):
    """
    统一表格基类

    现已集成列级别公式计算功能，所有继承此类的表格都将自动获得：
    1. 列公式设置和管理
    2. 自动计算和更新
    3. 公式配置保存和加载
    4. 统一的用户交互界面
    """

    # 通用信号定义
    record_selected = pyqtSignal(int)  # 记录选中信号
    record_added = pyqtSignal(int)     # 记录添加信号
    record_updated = pyqtSignal(int)   # 记录更新信号
    record_deleted = pyqtSignal(int)   # 记录删除信号

    # 公式相关信号
    formula_set = pyqtSignal(str, str, str)    # 表格名, 列名, 公式
    formula_cleared = pyqtSignal(str, str)     # 表格名, 列名
    formula_error = pyqtSignal(str, str)       # 列名, 错误信息
    data_calculated = pyqtSignal()             # 数据计算完成

    # 撤销重做相关信号
    undo_redo_state_changed = pyqtSignal(str, bool, bool)  # table_name, can_undo, can_redo

    def __init__(self, table_name="", parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.table_name = table_name

        # 数据模型 - 子类需要设置
        self.data_model = None

        # 字段配置 - 子类需要设置
        self.field_groups = {}

        # 显示设置 - 子类需要设置
        self.display_settings = {}

        # 公式计算引擎
        self.formula_engine = ColumnFormulaEngine()

        # 🔧 新增：公式配置管理器
        from src.formula.formula_config_manager import FormulaConfigManager
        self.formula_config_manager = FormulaConfigManager()

        # 数据缓存
        self.data_frame = pd.DataFrame()

        # 公式列标识
        self.formula_columns = set()

        # 公式功能启用标志
        self.formula_enabled = True

        # 🔧 新增：初始化撤销重做功能
        self._init_undo_redo()

        # 初始化UI
        self._init_ui()

        # 初始化多选和复制粘贴功能
        self._init_selection_handlers()

        # 初始化公式功能
        self._init_formula_features()

        # 连接数据计算完成信号
        self.data_calculated.connect(self._on_data_calculated)

        # 最后安装事件过滤器以处理键盘事件（确保优先级最高）
        self._install_keyboard_event_filter()

        # 防止递归的标志
        self._updating_display = False

    def _init_undo_redo(self):
        """初始化撤销重做功能"""
        try:
            # 导入撤销重做管理器
            from src.utils.undo_redo_manager import UndoRedoManager

            # 获取全局撤销重做管理器实例（如果不存在则创建）
            if not hasattr(BaseTableWidget, '_global_undo_redo_manager'):
                BaseTableWidget._global_undo_redo_manager = UndoRedoManager()

            self.undo_redo_manager = BaseTableWidget._global_undo_redo_manager

            self.logger.info(f"{self.table_name}撤销重做功能初始化完成")

        except Exception as e:
            self.logger.error(f"初始化{self.table_name}撤销重做功能失败: {e}")

    def _init_ui(self):
        """初始化UI - 子类可以重写"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 创建工具栏
        self._create_toolbar()
        layout.addWidget(self.toolbar)
        
        # 创建表格
        self._create_table()
        layout.addWidget(self.table)
    
    def _create_toolbar(self):
        """创建工具栏 - 子类可以重写"""
        self.toolbar = QToolBar(f"{self.table_name}工具栏")
        self.toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        # 新增记录
        self.add_action = QAction(ResourceLoader.load_icon("add.png"), "新增记录", self)
        self.add_action.triggered.connect(self.add_record)
        self.toolbar.addAction(self.add_action)
        
        # 删除记录
        self.delete_action = QAction(ResourceLoader.load_icon("delete.png"), "删除记录", self)
        self.delete_action.triggered.connect(self.delete_record)
        self.toolbar.addAction(self.delete_action)
        
        self.toolbar.addSeparator()
        
        # 刷新数据
        self.refresh_action = QAction(ResourceLoader.load_icon("refresh.png"), "刷新", self)
        self.refresh_action.triggered.connect(self.load_data)
        self.toolbar.addAction(self.refresh_action)
        
        # 导出数据
        self.export_action = QAction(ResourceLoader.load_icon("export.png"), "导出", self)
        self.export_action.triggered.connect(self.export_data)
        self.toolbar.addAction(self.export_action)
    
    def _create_table(self):
        """创建表格 - 通用表格设置"""
        self.table = QTableWidget()
        self.table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table.setSortingEnabled(False)

        # 设置表头
        self.table.horizontalHeader().setDefaultAlignment(Qt.AlignLeft | Qt.AlignVCenter | Qt.TextWordWrap)
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
        self.table.horizontalHeader().setMinimumSectionSize(60)

        # 设置垂直表头可见，便于行操作
        self.table.verticalHeader().setVisible(True)
        self.table.verticalHeader().setSectionResizeMode(QHeaderView.Interactive)

        # 设置选择模式 - 支持多选和单元格选择
        self.table.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.table.setSelectionBehavior(QAbstractItemView.SelectItems)

        # 设置编辑触发器：支持双击和键盘输入编辑
        self.table.setEditTriggers(QAbstractItemView.DoubleClicked |
                                  QAbstractItemView.EditKeyPressed)

        # 连接信号
        self.table.customContextMenuRequested.connect(self._show_context_menu)
        self.table.itemSelectionChanged.connect(self._on_selection_changed)
        self.table.cellChanged.connect(self._on_cell_changed)
        self.table.cellClicked.connect(self._on_cell_clicked)

        # 🔧 新增：连接编辑开始信号来记录旧值
        self.table.itemDoubleClicked.connect(self._on_item_double_clicked)

    def _init_selection_handlers(self):
        """初始化选择处理器"""
        try:
            # 导入表格选择处理器以支持多选、复制、粘贴和删除功能
            from src.utils.table_selection_handler import TableSelectionHandler
            self.table_selection_handler = TableSelectionHandler(self.table)

            # 导入多单元格选择管理器以增强Ctrl和Shift多选功能
            from src.utils.multi_cell_selection_manager import MultiCellSelectionManager
            self.multi_cell_selection_manager = MultiCellSelectionManager(self.table)

            # 导入行插入管理器以支持行插入功能
            from src.utils.row_insert_manager import RowInsertManager
            self.row_insert_manager = RowInsertManager(self.table, self)

            # 连接多选信号
            self._connect_multi_selection_signals()

            self.logger.info(f"{self.table_name}选择处理器初始化完成")

        except Exception as e:
            self.logger.error(f"初始化{self.table_name}选择处理器失败: {e}")

    def _install_keyboard_event_filter(self):
        """安装键盘事件过滤器（在所有其他过滤器之后安装，确保优先级最高）"""
        try:
            # 安装事件过滤器以处理键盘事件
            self.table.installEventFilter(self)
            self.logger.info(f"{self.table_name}键盘事件过滤器已安装")
        except Exception as e:
            self.logger.error(f"安装{self.table_name}键盘事件过滤器失败: {e}")

    def _connect_multi_selection_signals(self):
        """连接多选相关信号"""
        try:
            if hasattr(self, 'multi_cell_selection_manager'):
                # 连接Ctrl选择信号
                if hasattr(self.multi_cell_selection_manager, 'ctrl_selection'):
                    self.multi_cell_selection_manager.ctrl_selection.connect(self._on_ctrl_selection)
                # 连接Shift选择信号
                if hasattr(self.multi_cell_selection_manager, 'shift_selection'):
                    self.multi_cell_selection_manager.shift_selection.connect(self._on_shift_selection)
                # 连接范围选择信号（如果存在）
                if hasattr(self.multi_cell_selection_manager, 'range_selection'):
                    self.multi_cell_selection_manager.range_selection.connect(self._on_range_selection)

        except Exception as e:
            self.logger.error(f"连接{self.table_name}多选信号失败: {e}")

    def _on_ctrl_selection(self, row, col, selected_cells):
        """处理Ctrl多选"""
        self.logger.info(f"{self.table_name} Ctrl多选: 点击({row}, {col}), 当前选中{len(selected_cells)}个单元格")

    def _on_shift_selection(self, start_row, start_col, end_row, end_col, selected_cells):
        """处理Shift范围选择"""
        self.logger.info(f"{self.table_name} Shift范围选择: 从({start_row}, {start_col})到({end_row}, {end_col}), 选中{len(selected_cells)}个单元格")

    def _on_range_selection(self, start_row, start_col, end_row, end_col):
        """处理范围选择"""
        self.logger.info(f"{self.table_name} 范围选择: 从({start_row}, {start_col})到({end_row}, {end_col})")

    def eventFilter(self, source, event):
        """事件过滤器，用于处理表格的键盘编辑事件"""
        # 处理表格的键盘事件
        if source == self.table and event.type() == QEvent.KeyPress:
            # 检查是否是需要特殊处理的快捷键，让其他处理器优先处理
            if event.modifiers() == Qt.ControlModifier:
                if event.key() in (Qt.Key_C, Qt.Key_V, Qt.Key_A, Qt.Key_X):
                    # 这些快捷键应该由TableSelectionHandler处理，不要拦截
                    return super().eventFilter(source, event)
            elif event.key() == Qt.Key_Delete:
                # Delete键也应该由TableSelectionHandler处理
                return super().eventFilter(source, event)

            # 处理键盘输入和编辑事件
            handled = self._handle_table_key_press(event)
            if handled:
                return True

        # 处理输入法事件（中文输入）
        elif source == self.table and event.type() == QEvent.InputMethod:
            self.logger.debug(f"输入法事件: {event}")
            handled = self._handle_input_method_event(event)
            if handled:
                return True

        return super().eventFilter(source, event)

    def _handle_input_method_event(self, event):
        """处理输入法事件（中文输入）"""
        try:
            # 获取当前选中的单元格
            current_row = self.table.currentRow()
            current_col = self.table.currentColumn()

            if current_row < 0 or current_col < 0:
                return False

            # 获取输入法提交的文本
            commit_string = event.commitString()
            self.logger.debug(f"输入法提交文本: '{commit_string}', 长度={len(commit_string)}")

            if commit_string:
                # 检查是否尝试编辑公式列
                if self._is_formula_column(current_col):
                    header_item = self.table.horizontalHeaderItem(current_col)
                    column_name = header_item.text() if header_item else f"列{current_col}"
                    self.logger.info(f"公式列 '{column_name}' 不能直接编辑，请通过右键菜单设置公式")
                    return True

                # 检查是否有自定义控件
                widget = self.table.cellWidget(current_row, current_col)
                if widget:
                    return False

                # 检查普通单元格是否可编辑
                current_item = self.table.item(current_row, current_col)
                if current_item and (current_item.flags() & Qt.ItemIsEditable):
                    self.logger.debug(f"✅ 输入法文本通过检测: '{commit_string}'")

                    # 记录编辑前的值
                    self._cell_edit_old_value = current_item.text()

                    # 直接设置单元格内容为输入文字
                    current_item.setText(commit_string)

                    # 进入编辑模式
                    self.table.editItem(current_item)

                    # 设置光标位置
                    from PyQt5.QtCore import QTimer
                    from PyQt5.QtWidgets import QApplication

                    def fix_cursor_position():
                        focus_widget = QApplication.focusWidget()
                        if focus_widget and hasattr(focus_widget, 'setText') and hasattr(focus_widget, 'setCursorPosition'):
                            focus_widget.setText(commit_string)
                            focus_widget.setCursorPosition(len(commit_string))
                            if hasattr(focus_widget, 'deselect'):
                                focus_widget.deselect()
                            self.logger.debug(f"输入法设置编辑器: '{commit_string}', 光标位置: {len(commit_string)}")

                    QTimer.singleShot(10, fix_cursor_position)
                    return True

        except Exception as e:
            self.logger.error(f"处理输入法事件失败: {e}")

        return False

    def _should_trigger_edit_mode(self, event):
        """
        判断按键事件是否应该触发编辑模式

        参数:
            event: QKeyEvent对象

        返回:
            bool: True表示应该触发编辑模式，False表示不应该
        """
        # 获取按键代码和输入文本
        key = event.key()
        input_text = event.text()
        modifiers = event.modifiers()

        # 1. 明确排除不应该触发编辑的按键
        non_edit_keys = {
            Qt.Key_Up, Qt.Key_Down, Qt.Key_Left, Qt.Key_Right,  # 方向键
            Qt.Key_Alt, Qt.Key_Shift, Qt.Key_Control, Qt.Key_Meta,  # 修饰键
            Qt.Key_F1, Qt.Key_F2, Qt.Key_F3, Qt.Key_F4, Qt.Key_F5, Qt.Key_F6,  # 功能键
            Qt.Key_F7, Qt.Key_F8, Qt.Key_F9, Qt.Key_F10, Qt.Key_F11, Qt.Key_F12,
            Qt.Key_Escape, Qt.Key_Tab, Qt.Key_Backtab,  # 特殊控制键
            Qt.Key_Insert, Qt.Key_Delete, Qt.Key_Home, Qt.Key_End,
            Qt.Key_PageUp, Qt.Key_PageDown, Qt.Key_CapsLock,
            Qt.Key_NumLock, Qt.Key_ScrollLock, Qt.Key_Pause, Qt.Key_Print
        }

        if key in non_edit_keys:
            # 取消按键类通知日志打印
            return False

        # 2. 排除带有Ctrl或Alt修饰键的组合键（这些通常是快捷键）
        if modifiers & (Qt.ControlModifier | Qt.AltModifier):
            # 取消按键类通知日志打印
            return False

        # 3. 检查是否有有效的输入文本
        if not input_text or len(input_text) == 0:
            # 取消按键类通知日志打印
            return False

        # 4. 检查是否是可打印字符或有效的输入字符
        for char in input_text:
            # 支持可打印的ASCII字符
            if char.isprintable():
                # 取消按键类通知日志打印
                return True
            # 支持中文和其他Unicode字符（码点大于127）
            if ord(char) > 127:
                # 取消按键类通知日志打印
                return True
            # 支持字母数字字符
            if char.isalnum():
                # 取消按键类通知日志打印
                return True

        # 取消按键类通知日志打印
        return False

    def _handle_table_key_press(self, event):
        """处理表格的键盘按下事件 - 子类可以重写"""
        # 获取当前选中的单元格
        current_row = self.table.currentRow()
        current_col = self.table.currentColumn()

        if current_row < 0 or current_col < 0:
            return False

        # 检查是否是Esc键
        if event.key() == Qt.Key_Escape:
            # 如果当前单元格正在编辑，取消编辑
            if self.table.state() == QAbstractItemView.EditingState:
                current_item = self.table.currentItem()
                if current_item:
                    # 恢复原值
                    if hasattr(self, '_cell_edit_old_value'):
                        current_item.setText(self._cell_edit_old_value)
                        delattr(self, '_cell_edit_old_value')
                    # 结束编辑
                    self.table.closePersistentEditor(current_item)
                return True
            return False

        # 检查是否是Enter或Return键
        if event.key() in (Qt.Key_Return, Qt.Key_Enter):
            self.logger.debug(f"Enter键被按下，当前编辑状态: {self.table.state()}")

            # 如果当前单元格正在编辑，完成编辑并保存数据
            if self.table.state() == QAbstractItemView.EditingState:
                current_item = self.table.currentItem()
                if current_item:
                    self.logger.debug(f"准备保存编辑内容，行: {current_row}, 列: {current_col}")

                    # 获取编辑器中的当前内容 - 使用更可靠的方法
                    editor_content = None

                    # 方法1: 尝试通过QApplication获取焦点控件
                    from PyQt5.QtWidgets import QApplication
                    focus_widget = QApplication.focusWidget()
                    if focus_widget and hasattr(focus_widget, 'text'):
                        editor_content = focus_widget.text()
                        self.logger.debug(f"通过焦点控件获取编辑器内容: '{editor_content}'")

                    # 方法2: 如果方法1失败，尝试遍历所有子控件
                    if editor_content is None:
                        def find_editor(widget):
                            for child in widget.children():
                                if (hasattr(child, 'text') and hasattr(child, 'setText') and
                                    child.isVisible() and hasattr(child, 'hasFocus') and child.hasFocus()):
                                    return child.text()
                                # 递归查找
                                result = find_editor(child)
                                if result is not None:
                                    return result
                            return None

                        editor_content = find_editor(self.table)
                        if editor_content is not None:
                            self.logger.debug(f"通过递归查找获取编辑器内容: '{editor_content}'")

                    # 方法3: 如果还是没找到，尝试获取当前item的文本（可能已经更新了）
                    if editor_content is None:
                        editor_content = current_item.text()
                        self.logger.debug(f"使用当前item文本作为编辑器内容: '{editor_content}'")

                    # 如果找到编辑器内容，先更新到item
                    if editor_content is not None and editor_content != current_item.text():
                        current_item.setText(editor_content)
                        self.logger.debug(f"更新单元格内容为: '{editor_content}'")

                    # 记录编辑前后的值
                    old_value = getattr(self, '_cell_edit_old_value', '')
                    new_value = current_item.text()
                    self.logger.debug(f"编辑前值: '{old_value}', 编辑后值: '{new_value}'")

                    # 结束编辑，这会自动触发cellChanged信号保存数据
                    self.table.closePersistentEditor(current_item)
                    self.logger.debug("已调用 closePersistentEditor")

                    # 清理编辑状态
                    if hasattr(self, '_cell_edit_old_value'):
                        delattr(self, '_cell_edit_old_value')

                    # 移动到下一行的同一列
                    if current_row < self.table.rowCount() - 1:
                        self.table.setCurrentCell(current_row + 1, current_col)
                        self.logger.debug(f"移动到下一行: {current_row + 1}")
                return True
            else:
                self.logger.debug("不在编辑状态，Enter键移动到下一行")
                # 如果没有在编辑状态，Enter键移动到下一行
                if current_row < self.table.rowCount() - 1:
                    self.table.setCurrentCell(current_row + 1, current_col)
                return True

        # 使用新的键盘事件过滤逻辑判断是否应该触发编辑模式
        if self._should_trigger_edit_mode(event):
            input_text = event.text()
            self.logger.debug(f"✅ 输入文本通过检测: '{input_text}'")

            # 检查是否尝试编辑公式列
            if self._is_formula_column(current_col):
                # 显示提示信息
                header_item = self.table.horizontalHeaderItem(current_col)
                column_name = header_item.text() if header_item else f"列{current_col}"
                self.logger.info(f"公式列 '{column_name}' 不能直接编辑，请通过右键菜单设置公式")
                return True  # 阻止编辑

            # 检查是否有自定义控件
            widget = self.table.cellWidget(current_row, current_col)
            if widget:
                # 对于自定义控件，不处理键盘输入，让控件自己处理
                return False

            # 检查普通单元格是否可编辑
            current_item = self.table.item(current_row, current_col)
            if current_item and (current_item.flags() & Qt.ItemIsEditable):
                # 记录编辑前的值
                self._cell_edit_old_value = current_item.text()

                # 修复问题1：正确处理键盘输入进入编辑模式并保存第一个字符
                # 直接设置单元格内容为输入字符，然后进入编辑模式
                current_item.setText(input_text)

                # 进入编辑模式
                self.table.editItem(current_item)

                # 修复光标位置和文本选中问题
                from PyQt5.QtCore import QTimer
                from PyQt5.QtWidgets import QApplication

                def fix_cursor_position():
                    # 方法1: 通过QApplication获取焦点控件（最可靠）
                    focus_widget = QApplication.focusWidget()
                    if focus_widget and hasattr(focus_widget, 'setText') and hasattr(focus_widget, 'setCursorPosition'):
                        # 确保文本正确设置
                        focus_widget.setText(input_text)
                        # 将光标移到末尾，避免文本被选中
                        focus_widget.setCursorPosition(len(input_text))
                        # 取消文本选择
                        if hasattr(focus_widget, 'deselect'):
                            focus_widget.deselect()
                        self.logger.debug(f"通过焦点控件设置编辑器: '{input_text}', 光标位置: {len(input_text)}")
                        return

                    # 方法2: 递归查找编辑器（备用方案）
                    def find_editor(widget):
                        for child in widget.children():
                            if (hasattr(child, 'setText') and hasattr(child, 'text') and
                                hasattr(child, 'setCursorPosition') and child.isVisible()):
                                if hasattr(child, 'hasFocus') and child.hasFocus():
                                    return child
                            # 递归查找
                            result = find_editor(child)
                            if result:
                                return result
                        return None

                    editor = find_editor(self.table)
                    if editor:
                        editor.setText(input_text)
                        editor.setCursorPosition(len(input_text))
                        if hasattr(editor, 'deselect'):
                            editor.deselect()
                        self.logger.debug(f"通过递归查找设置编辑器: '{input_text}', 光标位置: {len(input_text)}")

                # 延迟执行以确保编辑器已创建
                QTimer.singleShot(10, fix_cursor_position)

                return True

        # 检查是否是Tab键
        if event.key() == Qt.Key_Tab:
            # 移动到下一列
            if current_col < self.table.columnCount() - 1:
                self.table.setCurrentCell(current_row, current_col + 1)
            else:
                # 如果是最后一列，移动到下一行的第一列
                if current_row < self.table.rowCount() - 1:
                    self.table.setCurrentCell(current_row + 1, 0)
            return True

        # 检查是否是Shift+Tab键
        if event.key() == Qt.Key_Backtab:
            # 移动到上一列
            if current_col > 0:
                self.table.setCurrentCell(current_row, current_col - 1)
            else:
                # 如果是第一列，移动到上一行的最后一列
                if current_row > 0:
                    self.table.setCurrentCell(current_row - 1, self.table.columnCount() - 1)
            return True

        return False

    def _show_context_menu(self, position):
        """显示右键菜单 - 子类可以重写"""
        menu = QMenu(self)
        
        # 添加记录
        add_action = menu.addAction("新增记录")
        add_action.triggered.connect(self.add_record)
        
        # 删除记录
        delete_action = menu.addAction("删除记录")
        delete_action.triggered.connect(self.delete_record)
        
        menu.addSeparator()
        
        # 复制
        copy_action = menu.addAction("复制")
        copy_action.triggered.connect(self.copy_selection)
        
        # 粘贴
        paste_action = menu.addAction("粘贴")
        paste_action.triggered.connect(self.paste_selection)
        
        menu.addSeparator()
        
        # 刷新
        refresh_action = menu.addAction("刷新")
        refresh_action.triggered.connect(self.load_data)
        
        menu.exec_(self.table.mapToGlobal(position))

    def _on_selection_changed(self):
        """选择变化处理 - 子类可以重写"""
        # 🔧 关键修复：使用selectionModel获取选中行，支持包含QComboBox的行选择
        selection_model = self.table.selectionModel()
        if selection_model:
            selected_indexes = selection_model.selectedRows()
            if selected_indexes:
                row = selected_indexes[0].row()
                self.record_selected.emit(row)
        else:
            # 回退到原有方法
            selected_items = self.table.selectedItems()
            if selected_items:
                row = selected_items[0].row()
                self.record_selected.emit(row)

    def _on_cell_changed(self, row, column):
        """单元格内容变化处理 - 记录撤销操作并处理数据变化"""
        try:
            # 防止在更新显示时触发数据保存
            if hasattr(self, '_updating_display') and self._updating_display:
                return

            # 获取当前单元格项
            item = self.table.item(row, column)
            if not item:
                return

            new_value = item.text()

            # 获取旧值（如果有记录的话）
            old_value = getattr(self, '_cell_edit_old_value', '')

            # 如果值没有变化，不记录操作
            if old_value == new_value:
                return

            # 获取字段名
            header_item = self.table.horizontalHeaderItem(column)
            field_name = header_item.text() if header_item else f"列{column}"

            # 记录撤销操作
            operation = {
                'type': 'cell_edit',
                'row': row,
                'col': column,
                'old_value': old_value,
                'new_value': new_value,
                'field_name': field_name,
                'timestamp': self._get_current_timestamp()
            }

            # 添加记录ID（如果可获取）
            if hasattr(self, '_get_record_id_for_row'):
                record_id = self._get_record_id_for_row(row)
                if record_id:
                    operation['record_id'] = record_id

            self.record_operation(operation)

            # 调用原有的处理逻辑
            self._handle_cell_change_with_formula_update(row, column)

            # 🔧 新增：更新状态提示
            self._update_status_message(field_name, new_value)

            # 清理编辑状态
            if hasattr(self, '_cell_edit_old_value'):
                delattr(self, '_cell_edit_old_value')

        except Exception as e:
            self.logger.error(f"{self.table_name}处理单元格变化失败: {e}")

    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def _update_status_message(self, field_name: str, new_value: str) -> None:
        """
        更新状态栏消息

        参数:
            field_name: 字段名称
            new_value: 新值
        """
        try:
            # 查找主窗口并更新状态
            main_window = self._find_main_window()
            if main_window and hasattr(main_window, 'update_status'):
                status_message = f"已更新{self.table_name}字段 '{field_name}' 的值"
                main_window.update_status(status_message)

        except Exception as e:
            self.logger.error(f"更新状态消息失败: {e}")

    def _find_main_window(self):
        """查找主窗口实例"""
        try:
            # 从当前组件向上查找主窗口
            widget = self
            while widget:
                if hasattr(widget, 'update_status') and hasattr(widget, 'tab_widget'):
                    return widget
                widget = widget.parent()
            return None
        except Exception as e:
            self.logger.error(f"查找主窗口失败: {e}")
            return None

    def _handle_cell_change_with_formula_update(self, row: int, column: int):
        """处理单元格变化并触发公式更新的通用方法"""
        try:
            # 防止在更新显示时触发数据保存
            if hasattr(self, '_updating_display') and self._updating_display:
                return

            # 获取字段名
            header_item = self.table.horizontalHeaderItem(column)
            if not header_item:
                return

            field_name = header_item.text()

            # 如果是公式列，不允许直接编辑，跳过保存
            if hasattr(self, 'formula_columns') and field_name in self.formula_columns:
                self.logger.debug(f"跳过公式列 {field_name} 的数据保存")
                return

            # 🔧 新增：获取单元格的新值并同步到数据框
            item = self.table.item(row, column)
            if item:
                new_value = item.text()
                self._sync_single_cell_to_dataframe(row, column, new_value)

            # 如果启用了公式功能，触发公式重新计算
            if (hasattr(self, 'formula_enabled') and self.formula_enabled and
                hasattr(self, 'formula_columns') and self.formula_columns):
                self._trigger_formula_recalculation(field_name, row)

        except Exception as e:
            self.logger.error(f"处理单元格变化失败: {e}")

    def _on_cell_clicked(self, row, column):
        """单元格点击处理 - 子类可以重写"""
        pass

    def _on_item_double_clicked(self, item):
        """单元格双击处理 - 记录编辑前的旧值"""
        try:
            if item:
                # 记录编辑前的值，用于撤销操作
                self._cell_edit_old_value = item.text()
                self.logger.debug(f"记录编辑前值: '{self._cell_edit_old_value}'")
        except Exception as e:
            self.logger.error(f"记录编辑前值失败: {e}")

    # 以下方法需要子类实现
    def load_data(self):
        """加载数据 - 子类必须实现"""
        raise NotImplementedError("子类必须实现load_data方法")

    def add_record(self):
        """添加记录 - 子类必须实现"""
        raise NotImplementedError("子类必须实现add_record方法")

    def delete_record(self):
        """删除记录 - 子类必须实现"""
        raise NotImplementedError("子类必须实现delete_record方法")

    def export_data(self):
        """导出数据 - 子类可以实现"""
        QMessageBox.information(self, "提示", "导出功能待实现")

    def copy_selection(self):
        """复制选中内容 - 子类可以重写"""
        if hasattr(self, 'table_selection_handler'):
            self.table_selection_handler.copy_selection()

    def paste_selection(self):
        """粘贴内容 - 子类可以重写"""
        if hasattr(self, 'table_selection_handler'):
            self.table_selection_handler.paste_selection()

    def _create_checkbox_cell_widget(self, checked=False):
        """创建复选框单元格控件"""
        checkbox = QCheckBox()
        checkbox.setChecked(checked)
        checkbox.setStyleSheet("QCheckBox { margin: 5px; }")
        
        # 创建容器widget以居中显示复选框
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.addWidget(checkbox)
        layout.setAlignment(Qt.AlignCenter)
        layout.setContentsMargins(0, 0, 0, 0)

        return container

    # ==================== 公式功能相关方法 ====================

    def _init_formula_features(self):
        """初始化公式功能"""
        # 🔧 初始化公式引擎和相关属性
        self.formula_engine = ColumnFormulaEngine()
        self.formula_columns = set()  # 存储公式列名

        # 🔧 性能优化：添加公式计算防抖机制
        self._formula_recalc_timer = None
        self._pending_formula_recalc = set()  # 待重新计算的列名

        if not self.formula_enabled:
            return

        try:
            # 设置表头右键菜单
            self.table.horizontalHeader().setContextMenuPolicy(Qt.CustomContextMenu)
            self.table.horizontalHeader().customContextMenuRequested.connect(
                self._show_header_context_menu
            )

            # 扩展现有右键菜单
            self._extend_context_menu_with_formula()

            # 连接数据变化信号
            self.table.itemChanged.connect(self._on_data_item_changed)

            self.logger.info(f"{self.table_name}公式功能初始化完成")

        except Exception as e:
            self.logger.error(f"初始化{self.table_name}公式功能失败: {e}")

    def _extend_context_menu_with_formula(self):
        """扩展右键菜单，添加公式相关选项"""
        try:
            if hasattr(self, 'context_menu') and self.context_menu:
                # 添加分隔符
                self.context_menu.addSeparator()

                # 创建公式子菜单
                formula_menu = self.context_menu.addMenu("📊 列公式")

                # 设置列公式
                set_formula_action = QAction("设置列公式", self)
                set_formula_action.triggered.connect(self._show_column_formula_dialog)
                formula_menu.addAction(set_formula_action)

                # 清除列公式
                clear_formula_action = QAction("清除列公式", self)
                clear_formula_action.triggered.connect(self._clear_column_formula)
                formula_menu.addAction(clear_formula_action)

                # 重新计算
                recalc_action = QAction("重新计算", self)
                recalc_action.triggered.connect(self._recalculate_formulas)
                formula_menu.addAction(recalc_action)

                # 查看所有公式
                view_formulas_action = QAction("查看所有公式", self)
                view_formulas_action.triggered.connect(self._show_all_formulas)
                formula_menu.addAction(view_formulas_action)

        except Exception as e:
            self.logger.error(f"扩展{self.table_name}右键菜单失败: {e}")

    def _show_header_context_menu(self, position):
        """显示表头右键菜单"""
        if not self.formula_enabled:
            return

        try:
            # 获取点击的列
            logical_index = self.table.horizontalHeader().logicalIndexAt(position)
            if logical_index >= 0:
                self.current_header_column = logical_index

                # 获取列名
                header_item = self.table.horizontalHeaderItem(logical_index)
                if header_item:
                    column_name = header_item.text()

                    # 创建表头菜单
                    header_menu = QMenu(self)

                    # 设置列公式
                    set_formula_action = QAction(f"设置 '{column_name}' 列公式", self)
                    set_formula_action.triggered.connect(self._show_column_formula_dialog)
                    header_menu.addAction(set_formula_action)

                    # 如果是公式列，添加更多选项
                    if column_name in self.formula_columns:
                        clear_formula_action = QAction(f"清除 '{column_name}' 列公式", self)
                        clear_formula_action.triggered.connect(self._clear_column_formula)
                        header_menu.addAction(clear_formula_action)

                        view_formula_action = QAction(f"查看 '{column_name}' 列公式", self)
                        view_formula_action.triggered.connect(self._view_column_formula)
                        header_menu.addAction(view_formula_action)

                    # 显示菜单
                    global_pos = self.table.horizontalHeader().mapToGlobal(position)
                    header_menu.exec_(global_pos)

        except Exception as e:
            self.logger.error(f"显示{self.table_name}表头菜单失败: {e}")

    def _on_data_item_changed(self, item):
        """数据项变化处理 - 只对影响公式列的变化进行处理"""
        # 防止递归调用
        if self._updating_display:
            return

        if not self.formula_enabled or not self.formula_columns:
            return

        try:
            # 获取变化的列名
            if item is None:
                return

            column_index = item.column()
            if column_index < 0 or column_index >= self.table.columnCount():
                return

            header_item = self.table.horizontalHeaderItem(column_index)
            if not header_item:
                return

            changed_column_name = header_item.text()

            # 如果变化的是公式列本身，不需要重新计算（避免循环）
            if changed_column_name in self.formula_columns:
                return

            # 触发公式重新计算
            self._trigger_formula_recalculation(changed_column_name, item.row())

        except Exception as e:
            self.logger.error(f"{self.table_name}数据变化处理失败: {e}")

    def _trigger_formula_recalculation(self, changed_column_name: str, changed_row: int = -1):
        """触发公式重新计算的统一入口 - 带防抖机制"""
        try:
            # 检查是否需要重新计算公式列
            needs_recalculation = False
            affected_formulas = []

            # 检查是否有公式列依赖于这个变化的列
            for formula_column in self.formula_columns:
                formula_info = self.formula_engine.get_column_formulas(self.table_name).get(formula_column)
                if formula_info and changed_column_name in formula_info.dependencies:
                    needs_recalculation = True
                    affected_formulas.append(formula_column)

            # 只有当变化影响到公式列时才重新计算
            if needs_recalculation:
                self.logger.debug(f"列 {changed_column_name} 的变化将影响公式列: {affected_formulas}")
                # 如果指定了行号，可以进行更精确的更新（预留接口）
                if changed_row >= 0:
                    self.logger.debug(f"变化发生在第 {changed_row} 行")

                # 🔧 性能优化：使用防抖机制，避免频繁重新计算
                self._pending_formula_recalc.add(changed_column_name)
                self._debounced_formula_recalculation()

        except Exception as e:
            self.logger.error(f"触发公式重新计算失败: {e}")

    def _debounced_formula_recalculation(self):
        """防抖的公式重新计算"""
        try:
            # 🔧 如果已有定时器在运行，取消它
            if self._formula_recalc_timer is not None:
                self._formula_recalc_timer.stop()
                self._formula_recalc_timer.deleteLater()

            # 🔧 创建新的定时器，延迟执行公式重新计算
            from PyQt5.QtCore import QTimer
            self._formula_recalc_timer = QTimer()
            self._formula_recalc_timer.setSingleShot(True)
            self._formula_recalc_timer.timeout.connect(self._execute_pending_formula_recalculation)
            self._formula_recalc_timer.start(100)  # 100ms延迟，可以根据需要调整

        except Exception as e:
            self.logger.error(f"防抖公式重新计算失败: {e}")

    def _execute_pending_formula_recalculation(self):
        """执行待处理的公式重新计算"""
        try:
            if not self._pending_formula_recalc:
                return

            # 🔧 获取所有待处理的列名
            changed_columns = list(self._pending_formula_recalc)
            self._pending_formula_recalc.clear()

            # 🔧 批量处理所有变化的列
            for changed_column_name in changed_columns:
                self._recalculate_dependent_formulas(changed_column_name)

            # 🔧 清理定时器
            if self._formula_recalc_timer is not None:
                self._formula_recalc_timer.deleteLater()
                self._formula_recalc_timer = None

        except Exception as e:
            self.logger.error(f"执行待处理的公式重新计算失败: {e}")

    def _recalculate_dependent_formulas(self, changed_column_name: str):
        """重新计算依赖于指定列的公式列 - 优化版本"""
        try:
            if self.data_frame.empty:
                return

            # 🔧 关键修复：先同步表格数据到数据框，确保计算基于最新数据
            self._sync_table_to_dataframe()

            # 🔧 关键修复：清除公式计算缓存，确保使用最新数据重新计算
            self.formula_engine.clear_cache(self.table_name)

            # 🔧 性能优化：收集所有需要重新计算的公式列
            affected_formula_columns = []
            for formula_column in self.formula_columns:
                formula_info = self.formula_engine.get_column_formulas(self.table_name).get(formula_column)
                if formula_info and changed_column_name in formula_info.dependencies:
                    affected_formula_columns.append(formula_column)

            if not affected_formula_columns:
                return

            self.logger.info(f"列 {changed_column_name} 的变化将重新计算公式列: {affected_formula_columns}")

            # 🔧 批量计算所有受影响的公式列
            for formula_column in affected_formula_columns:
                # 重新计算这个公式列
                calculated_series = self.formula_engine.calculate_column(
                    self.table_name, formula_column, self.data_frame
                )

                # 更新数据框
                self.data_frame[formula_column] = calculated_series

                # 更新表格显示（只更新这一列）
                self._update_table_column_display(formula_column, calculated_series)

                # 保存公式计算结果到数据库
                self._save_formula_results_to_database(formula_column, calculated_series)

                self.logger.info(f"重新计算公式列 {formula_column} 完成，共 {len(calculated_series)} 行")

        except Exception as e:
            self.logger.error(f"重新计算依赖公式失败: {e}")

    def _sync_table_to_dataframe(self):
        """将表格中的当前数据同步到数据框"""
        try:
            if self.data_frame.empty:
                return

            # 🔧 改进：遍历表格中的所有数据，同步到数据框
            for row in range(self.table.rowCount()):
                for col in range(self.table.columnCount()):
                    if col < len(self.data_frame.columns) and row < len(self.data_frame):
                        header_item = self.table.horizontalHeaderItem(col)
                        if header_item:
                            column_name = header_item.text()

                            # 跳过公式列，它们的值由公式计算
                            if column_name in self.formula_columns:
                                continue

                            # 获取表格中的值
                            item = self.table.item(row, col)
                            if item:
                                value = item.text()
                                # 🔧 改进：更好的数据类型转换逻辑
                                try:
                                    # 如果原始数据是数字类型，尝试转换
                                    if column_name in self.data_frame.columns and pd.api.types.is_numeric_dtype(self.data_frame[column_name]):
                                        if value.strip() == "":
                                            value = None
                                        else:
                                            value = pd.to_numeric(value, errors='coerce')
                                except Exception:
                                    pass  # 保持字符串类型

                                # 🔧 改进：使用列名而不是列索引来更新数据
                                if column_name in self.data_frame.columns:
                                    self.data_frame.loc[row, column_name] = value

            self.logger.debug(f"已同步表格数据到数据框，共 {len(self.data_frame)} 行数据")

        except Exception as e:
            self.logger.error(f"同步表格数据到数据框失败: {e}")

    def _sync_single_cell_to_dataframe(self, row: int, column: int, value: str):
        """将单个单元格的数据同步到数据框"""
        try:
            if self.data_frame.empty or row >= len(self.data_frame):
                return

            header_item = self.table.horizontalHeaderItem(column)
            if not header_item:
                return

            column_name = header_item.text()

            # 跳过公式列，它们的值由公式计算
            if column_name in self.formula_columns:
                return

            # 🔧 新增：数据类型转换逻辑
            try:
                if column_name in self.data_frame.columns and pd.api.types.is_numeric_dtype(self.data_frame[column_name]):
                    if value.strip() == "":
                        value = None
                    else:
                        value = pd.to_numeric(value, errors='coerce')
            except Exception:
                pass  # 保持字符串类型

            # 更新数据框中的值
            if column_name in self.data_frame.columns:
                self.data_frame.loc[row, column_name] = value
                self.logger.debug(f"已同步单元格数据到数据框: 行{row}, 列{column_name}, 值'{value}'")

        except Exception as e:
            self.logger.error(f"同步单元格数据到数据框失败: {e}")

    def _save_formula_results_to_database(self, formula_column: str, calculated_series: pd.Series):
        """将公式计算结果保存到数据库 - 子类可以重写此方法"""
        try:
            # 基类提供默认实现，子类可以根据具体的数据模型重写
            # 这里只是记录日志，具体的保存逻辑由子类实现
            self.logger.debug(f"公式列 {formula_column} 计算完成，共 {len(calculated_series)} 行结果")

            # 子类应该重写此方法来实现具体的数据库保存逻辑
            # 例如：
            # for row_index, value in calculated_series.items():
            #     record_id = self._get_record_id_for_row(row_index)
            #     if record_id:
            #         self._update_record_in_database(record_id, {formula_column: value})

        except Exception as e:
            self.logger.error(f"保存公式结果到数据库失败: {e}")

    def _get_record_id_for_row(self, row_index: int):
        """获取指定行的记录ID - 子类应该重写此方法"""
        # 基类提供默认实现，尝试从表格中获取ID
        try:
            # 通常ID在第一列或第二列
            for col in [0, 1]:
                if col < self.table.columnCount():
                    header_item = self.table.horizontalHeaderItem(col)
                    if header_item and header_item.text().lower() in ['id', 'record_id']:
                        id_item = self.table.item(row_index, col)
                        if id_item and id_item.text().isdigit():
                            return int(id_item.text())
            return None
        except Exception as e:
            self.logger.error(f"获取行{row_index}的记录ID失败: {e}")
            return None

    def _update_record_in_database(self, record_id: int, data: dict):
        """更新数据库中的记录 - 子类应该重写此方法"""
        # 基类提供默认实现，子类应该根据具体的数据模型重写
        self.logger.debug(f"需要更新记录 {record_id}: {data}")
        pass

    def _show_column_formula_dialog(self):
        """显示列公式编辑对话框"""
        if not hasattr(self, 'current_header_column'):
            return

        try:
            column_index = self.current_header_column
            header_item = self.table.horizontalHeaderItem(column_index)
            if not header_item:
                return

            column_name = header_item.text()

            # 创建公式编辑对话框
            dialog = ColumnFormulaDialog(self)
            dialog.set_column_info(
                table_name=self.table_name,
                column_name=column_name,
                available_columns=self._get_available_columns()
            )

            # 获取当前公式
            current_formula = self._get_column_formula(column_name)
            dialog.set_formula(current_formula)

            if dialog.exec_() == QDialog.Accepted:
                formula, description = dialog.get_formula_data()
                self._set_column_formula(column_name, formula, description)

        except Exception as e:
            self.logger.error(f"显示{self.table_name}公式对话框失败: {e}")

    def _get_available_columns(self):
        """获取可用列名列表"""
        columns = []
        for i in range(self.table.columnCount()):
            header_item = self.table.horizontalHeaderItem(i)
            if header_item:
                columns.append(header_item.text())
        return columns

    def _is_formula_column(self, column_index: int) -> bool:
        """检查指定列索引是否为公式列"""
        try:
            # 获取列头项
            header_item = self.table.horizontalHeaderItem(column_index)
            if not header_item:
                return False

            # 获取列名
            column_name = header_item.text()

            # 检查是否在公式列集合中
            return hasattr(self, 'formula_columns') and column_name in self.formula_columns
        except Exception as e:
            self.logger.error(f"检查公式列失败: {e}")
            return False

    def _get_column_formula(self, column_name: str) -> str:
        """获取列公式"""
        column_formulas = self.formula_engine.get_column_formulas(self.table_name)
        if column_name in column_formulas:
            return column_formulas[column_name].formula_template
        return ""

    def _set_column_formula(self, column_name: str, formula: str, description: str):
        """设置列公式"""
        try:
            if self.formula_engine.set_column_formula(
                self.table_name, column_name, formula, description):

                # 标记为公式列
                self.formula_columns.add(column_name)

                # 重新计算并更新显示
                self._recalculate_and_update_column(column_name)

                # 更新列头样式
                self._update_column_header_style(column_name, True)

                # 发送信号
                self.formula_set.emit(self.table_name, column_name, formula)

                # 🔧 新增：自动保存公式配置
                self._auto_save_formula_config()

                self.logger.info(f"列公式设置成功: {self.table_name}.{column_name}")
                QMessageBox.information(self, "成功", f"列 '{column_name}' 的公式设置成功")
            else:
                self.formula_error.emit(column_name, "公式设置失败")
                QMessageBox.warning(self, "错误", f"列 '{column_name}' 的公式设置失败")

        except Exception as e:
            self.logger.error(f"设置{self.table_name}列公式失败: {e}")
            QMessageBox.warning(self, "错误", f"设置公式时发生错误: {str(e)}")

    def _clear_column_formula(self):
        """清除列公式"""
        if not hasattr(self, 'current_header_column'):
            return

        try:
            column_index = self.current_header_column
            header_item = self.table.horizontalHeaderItem(column_index)
            if not header_item:
                return

            column_name = header_item.text()

            if self.formula_engine.remove_column_formula(self.table_name, column_name):
                # 移除公式列标记
                self.formula_columns.discard(column_name)

                # 🔧 修复：从配置文件中移除公式配置
                if hasattr(self, 'formula_config_manager'):
                    self.formula_config_manager.remove_column_formula_from_config(self.table_name, column_name)

                # 更新列头样式
                self._update_column_header_style(column_name, False)

                # 重新加载数据
                self.load_data()

                # 发送信号
                self.formula_cleared.emit(self.table_name, column_name)

                # 🔧 修复：保存更新后的公式配置（确保清除操作持久化）
                self._auto_save_formula_config()

                self.logger.info(f"列公式已清除: {self.table_name}.{column_name}")
                QMessageBox.information(self, "成功", f"列 '{column_name}' 的公式已清除")
            else:
                QMessageBox.warning(self, "错误", f"清除列 '{column_name}' 的公式失败")

        except Exception as e:
            self.logger.error(f"清除{self.table_name}列公式失败: {e}")
            QMessageBox.warning(self, "错误", f"清除公式时发生错误: {str(e)}")

    def _view_column_formula(self):
        """查看列公式"""
        if not hasattr(self, 'current_header_column'):
            return

        try:
            column_index = self.current_header_column
            header_item = self.table.horizontalHeaderItem(column_index)
            if not header_item:
                return

            column_name = header_item.text()
            formula = self._get_column_formula(column_name)

            if formula:
                # 获取公式详细信息
                column_formulas = self.formula_engine.get_column_formulas(self.table_name)
                if column_name in column_formulas:
                    formula_obj = column_formulas[column_name]

                    info_text = f"""
列名: {column_name}
公式: {formula_obj.formula_template}
描述: {formula_obj.description}
依赖列: {', '.join(formula_obj.dependencies)}
状态: {'启用' if formula_obj.is_enabled else '禁用'}
创建时间: {formula_obj.created_time}
更新时间: {formula_obj.updated_time}
                    """.strip()

                    QMessageBox.information(self, "列公式信息", info_text)
            else:
                QMessageBox.information(self, "列公式信息", f"列 '{column_name}' 没有设置公式")

        except Exception as e:
            self.logger.error(f"查看{self.table_name}列公式失败: {e}")

    def _recalculate_formulas(self):
        """重新计算所有公式"""
        if not self.formula_enabled or not self.formula_columns:
            return

        try:
            # 清除缓存
            self.formula_engine.clear_cache(self.table_name)

            # 如果有数据，重新计算公式
            if not self.data_frame.empty:
                self.data_frame = self.formula_engine.calculate_all_formula_columns(
                    self.table_name, self.data_frame
                )

            # 发送信号，触发表格刷新
            self.data_calculated.emit()

            self.logger.info(f"{self.table_name}公式重新计算完成")

        except Exception as e:
            self.logger.error(f"{self.table_name}重新计算公式失败: {e}")
            # 即使出错也要发送信号
            self.data_calculated.emit()

    def _show_all_formulas(self):
        """显示所有公式"""
        try:
            column_formulas = self.formula_engine.get_column_formulas(self.table_name)

            if not column_formulas:
                QMessageBox.information(self, "公式列表", f"表格 '{self.table_name}' 没有设置任何公式")
                return

            # 构建公式列表文本
            formula_list = []
            for column_name, formula_obj in column_formulas.items():
                status = "启用" if formula_obj.is_enabled else "禁用"
                formula_list.append(f"• {column_name}: {formula_obj.formula_template} ({status})")

            formula_text = f"表格 '{self.table_name}' 的公式列表:\n\n" + "\n".join(formula_list)

            QMessageBox.information(self, "所有公式", formula_text)

        except Exception as e:
            self.logger.error(f"显示{self.table_name}所有公式失败: {e}")

    def _recalculate_and_update_column(self, column_name: str):
        """重新计算并更新指定列"""
        if self.data_frame.empty:
            return

        try:
            # 计算列值
            calculated_series = self.formula_engine.calculate_column(
                self.table_name, column_name, self.data_frame
            )

            # 更新数据框
            self.data_frame[column_name] = calculated_series

            # 更新表格显示
            self._update_table_column_display(column_name, calculated_series)

        except Exception as e:
            self.logger.error(f"重新计算{self.table_name}列{column_name}失败: {e}")

    def _update_table_column_display(self, column_name: str, series: pd.Series):
        """更新表格列显示"""
        try:
            # 找到列索引
            column_index = -1
            for i in range(self.table.columnCount()):
                header_item = self.table.horizontalHeaderItem(i)
                if header_item and header_item.text() == column_name:
                    column_index = i
                    break

            if column_index == -1:
                self.logger.warning(f"未找到列 {column_name}")
                return

            # 确保表格有足够的行
            if self.table.rowCount() < len(series):
                self.table.setRowCount(len(series))

            # 更新列数据
            for row in range(len(series)):
                if row >= self.table.rowCount():
                    break

                value = series.iloc[row]

                # 安全地获取或创建表格项
                try:
                    item = self.table.item(row, column_index)
                    if not item or item is None:
                        item = QTableWidgetItem()
                        self.table.setItem(row, column_index, item)

                    # 检查item是否仍然有效
                    if item is None:
                        continue

                    # 设置值和样式
                    if isinstance(value, str) and value.startswith("#"):
                        # 错误值
                        item.setText(value)
                        item.setBackground(QColor("#ffebee"))
                        item.setForeground(QColor("#d32f2f"))
                    else:
                        item.setText(str(value) if pd.notna(value) else "")
                        item.setBackground(QColor("#f3e5f5"))  # 公式列背景色
                        item.setForeground(QColor("#7b1fa2"))

                except RuntimeError as runtime_error:
                    # Qt对象已被删除
                    self.logger.warning(f"表格项已被删除，跳过行 {row}: {runtime_error}")
                    continue

        except Exception as e:
            self.logger.error(f"更新{self.table_name}列显示失败: {e}")

    def _update_column_header_style(self, column_name: str, is_formula: bool):
        """更新列头样式"""
        try:
            # 找到列索引
            for i in range(self.table.columnCount()):
                header_item = self.table.horizontalHeaderItem(i)
                if header_item and header_item.text() == column_name:
                    if is_formula:
                        header_item.setBackground(QColor("#e8f5e8"))
                        header_item.setForeground(QColor("#2e7d32"))
                        header_item.setToolTip(f"公式列: {column_name}")
                    else:
                        header_item.setBackground(QColor("#f5f5f5"))
                        header_item.setForeground(QColor("black"))
                        header_item.setToolTip("")
                    break

        except Exception as e:
            self.logger.error(f"更新{self.table_name}列头样式失败: {e}")

    def set_data_frame(self, df: pd.DataFrame):
        """设置数据框并触发公式计算"""
        try:
            if df.empty:
                self.data_frame = pd.DataFrame()
                self.data_calculated.emit()
                return

            self.data_frame = df.copy()

            # 如果有公式列，重新计算
            if self.formula_enabled and self.formula_columns:
                self.data_frame = self.formula_engine.calculate_all_formula_columns(
                    self.table_name, self.data_frame
                )

            # 发送数据计算完成信号
            self.data_calculated.emit()

        except Exception as e:
            self.logger.error(f"设置{self.table_name}数据框失败: {e}")
            # 即使出错也要发送信号，避免界面卡死
            self.data_calculated.emit()

    def get_data_frame(self) -> pd.DataFrame:
        """获取数据框"""
        return self.data_frame.copy() if not self.data_frame.empty else pd.DataFrame()

    def enable_formula_features(self, enabled: bool = True):
        """启用或禁用公式功能"""
        self.formula_enabled = enabled
        if enabled and not hasattr(self, 'formula_engine'):
            self.formula_engine = ColumnFormulaEngine()
            self._init_formula_features()

        self.logger.info(f"{self.table_name}公式功能{'启用' if enabled else '禁用'}")

    def load_formula_config(self):
        """加载公式配置 - 子类可以重写此方法"""
        pass

    def save_formula_config(self):
        """保存公式配置 - 子类可以重写此方法"""
        pass

    def _auto_save_formula_config(self):
        """自动保存公式配置"""
        try:
            # 调用子类的保存方法
            if hasattr(self, 'save_formula_config') and callable(self.save_formula_config):
                success = self.save_formula_config()
                if success:
                    self.logger.info(f"{self.table_name}公式配置自动保存成功")
                else:
                    self.logger.warning(f"{self.table_name}公式配置自动保存失败")
            else:
                self.logger.warning(f"{self.table_name}没有实现save_formula_config方法")
        except Exception as e:
            self.logger.error(f"{self.table_name}自动保存公式配置时发生异常: {e}")

    def _on_data_calculated(self):
        """数据计算完成后的处理"""
        try:
            # 重新加载表格显示
            self._refresh_table_display()

            # 🔧 **关键修复**：数据加载完成后重新安装QComboBox事件过滤器
            # 这确保新创建的QComboBox控件能够正确处理多选事件
            if hasattr(self, 'table_selection_handler') and self.table_selection_handler:
                self.table_selection_handler.force_reinstall_all_combobox_filters()
                self.logger.debug(f"{self.table_name}数据加载完成，已重新安装QComboBox事件过滤器")

        except Exception as e:
            self.logger.error(f"{self.table_name}数据计算完成处理失败: {e}")

    def _refresh_table_display(self):
        """刷新表格显示"""
        try:
            if self.data_frame.empty:
                return

            # 设置更新标志，防止递归
            self._updating_display = True

            # 阻止表格信号，防止触发cellChanged和itemChanged
            self.table.blockSignals(True)

            # 确保表格有足够的行和列
            self.table.setRowCount(len(self.data_frame))
            self.table.setColumnCount(len(self.data_frame.columns))

            # 设置列头
            self.table.setHorizontalHeaderLabels(list(self.data_frame.columns))

            # 填充数据
            for row in range(len(self.data_frame)):
                for col in range(len(self.data_frame.columns)):
                    value = self.data_frame.iloc[row, col]
                    column_name = self.data_frame.columns[col]

                    # 检查是否是公式列
                    is_formula_column = column_name in self.formula_columns

                    # 获取现有的控件或表格项
                    existing_widget = self.table.cellWidget(row, col)
                    existing_item = self.table.item(row, col)

                    if is_formula_column:
                        # 公式列：只更新值，设置为只读
                        if existing_widget:
                            # 如果有控件，移除它并创建只读的表格项
                            self.table.removeCellWidget(row, col)
                            item = QTableWidgetItem()
                            self.table.setItem(row, col, item)
                        else:
                            # 创建或获取表格项
                            item = existing_item if existing_item else QTableWidgetItem()
                            if not existing_item:
                                self.table.setItem(row, col, item)

                        # 设置值
                        display_value = str(value) if pd.notna(value) else ""
                        item.setText(display_value)

                        # 设置为只读
                        from PyQt5.QtCore import Qt
                        item.setFlags(item.flags() & ~Qt.ItemIsEditable)

                        # 设置公式列样式和工具提示
                        if isinstance(value, str) and value.startswith("#"):
                            # 错误值
                            item.setBackground(QColor("#ffebee"))
                            item.setForeground(QColor("#d32f2f"))
                            item.setToolTip(f"公式计算错误: {value}")
                        else:
                            # 正常公式值
                            item.setBackground(QColor("#f3e5f5"))
                            item.setForeground(QColor("#7b1fa2"))
                            # 获取公式信息用于工具提示
                            formula_info = self.formula_engine.get_column_formulas(self.table_name).get(column_name)
                            if formula_info:
                                item.setToolTip(f"公式列: {formula_info.formula_template}\n计算结果: {value}")
                            else:
                                item.setToolTip(f"公式列 (计算结果: {value})")
                    else:
                        # 非公式列：保持可编辑状态，保留原有控件
                        if existing_widget:
                            # 如果有控件，更新控件的值
                            self._update_widget_value(existing_widget, value)
                        else:
                            # 如果是表格项，更新值并确保可编辑
                            item = existing_item if existing_item else QTableWidgetItem()
                            if not existing_item:
                                self.table.setItem(row, col, item)

                            # 设置值
                            display_value = str(value) if pd.notna(value) else ""
                            item.setText(display_value)

                            # 确保可编辑
                            from PyQt5.QtCore import Qt
                            item.setFlags(item.flags() | Qt.ItemIsEditable)

                            # 恢复默认样式
                            item.setBackground(QColor("#ffffff"))
                            item.setForeground(QColor("#000000"))

            # 更新列头样式
            for column_name in self.formula_columns:
                self._update_column_header_style(column_name, True)

            self.logger.info(f"{self.table_name}表格显示刷新完成")

        except Exception as e:
            self.logger.error(f"{self.table_name}刷新表格显示失败: {e}")
        finally:
            # 重新启用表格信号
            self.table.blockSignals(False)
            # 清除更新标志
            self._updating_display = False

    # ==================== 撤销重做功能 ====================

    def record_operation(self, operation: dict) -> None:
        """
        记录一个操作到撤销栈

        参数:
            operation: 操作信息字典，应包含type、相关数据等
        """
        try:
            if not self.table_name:
                self.logger.warning("表格名称为空，无法记录操作")
                return

            # 记录操作到全局管理器
            self.undo_redo_manager.record_operation(self.table_name, operation)

            # 通知状态变化
            self._notify_undo_redo_state_changed()

        except Exception as e:
            self.logger.error(f"记录{self.table_name}操作失败: {e}")

    def can_undo(self) -> bool:
        """检查是否可以撤销"""
        return self.undo_redo_manager.can_undo(self.table_name)

    def can_redo(self) -> bool:
        """检查是否可以重做"""
        return self.undo_redo_manager.can_redo(self.table_name)

    def undo(self) -> bool:
        """
        执行撤销操作

        返回:
            bool: 是否成功执行撤销
        """
        try:
            if not self.can_undo():
                self.logger.debug(f"{self.table_name}没有可撤销的操作")
                return False

            # 获取撤销操作
            operation = self.undo_redo_manager.pop_undo_operation(self.table_name)
            if not operation:
                return False

            # 暂时阻止表格信号，避免在撤销过程中触发新的记录
            original_signals_blocked = self.table.signalsBlocked()
            self.table.blockSignals(True)

            try:
                # 执行具体的撤销操作
                success = self._execute_undo_operation(operation)

                if success:
                    # 将操作推入重做栈
                    self.undo_redo_manager.push_redo_operation(self.table_name, operation)
                    self.logger.info(f"{self.table_name}撤销操作成功: {operation.get('type', '未知类型')}")
                else:
                    # 如果撤销失败，将操作重新放回撤销栈
                    self.undo_redo_manager.record_operation(self.table_name, operation)
                    self.logger.warning(f"{self.table_name}撤销操作失败")

                return success

            finally:
                # 恢复表格信号状态
                self.table.blockSignals(original_signals_blocked)
                # 通知状态变化
                self._notify_undo_redo_state_changed()

        except Exception as e:
            self.logger.error(f"{self.table_name}撤销操作异常: {e}")
            return False

    def redo(self) -> bool:
        """
        执行重做操作

        返回:
            bool: 是否成功执行重做
        """
        try:
            if not self.can_redo():
                self.logger.debug(f"{self.table_name}没有可重做的操作")
                return False

            # 获取重做操作
            operation = self.undo_redo_manager.pop_redo_operation(self.table_name)
            if not operation:
                return False

            # 暂时阻止表格信号，避免在重做过程中触发新的记录
            original_signals_blocked = self.table.signalsBlocked()
            self.table.blockSignals(True)

            try:
                # 执行具体的重做操作
                success = self._execute_redo_operation(operation)

                if success:
                    # 将操作推入撤销栈
                    self.undo_redo_manager.record_operation(self.table_name, operation)
                    self.logger.info(f"{self.table_name}重做操作成功: {operation.get('type', '未知类型')}")
                else:
                    # 如果重做失败，将操作重新放回重做栈
                    self.undo_redo_manager.push_redo_operation(self.table_name, operation)
                    self.logger.warning(f"{self.table_name}重做操作失败")

                return success

            finally:
                # 恢复表格信号状态
                self.table.blockSignals(original_signals_blocked)
                # 通知状态变化
                self._notify_undo_redo_state_changed()

        except Exception as e:
            self.logger.error(f"{self.table_name}重做操作异常: {e}")
            return False

    def _execute_undo_operation(self, operation: dict) -> bool:
        """
        执行具体的撤销操作 - 子类可以重写以支持特定的操作类型

        参数:
            operation: 操作信息字典

        返回:
            bool: 是否成功执行
        """
        operation_type = operation.get('type', '')

        if operation_type == 'cell_edit':
            return self._undo_cell_edit(operation)
        elif operation_type == 'table_state':
            return self._undo_table_state(operation)
        elif operation_type == 'batch_operation':
            return self._undo_batch_operation(operation)
        else:
            self.logger.warning(f"{self.table_name}不支持的撤销操作类型: {operation_type}")
            return False

    def _execute_redo_operation(self, operation: dict) -> bool:
        """
        执行具体的重做操作 - 子类可以重写以支持特定的操作类型

        参数:
            operation: 操作信息字典

        返回:
            bool: 是否成功执行
        """
        operation_type = operation.get('type', '')

        if operation_type == 'cell_edit':
            return self._redo_cell_edit(operation)
        elif operation_type == 'table_state':
            return self._redo_table_state(operation)
        elif operation_type == 'batch_operation':
            return self._redo_batch_operation(operation)
        else:
            self.logger.warning(f"{self.table_name}不支持的重做操作类型: {operation_type}")
            return False

    def _undo_cell_edit(self, operation: dict) -> bool:
        """撤销单元格编辑操作"""
        try:
            row = operation.get('row', -1)
            col = operation.get('col', -1)
            old_value = operation.get('old_value', '')

            # 检查行列是否有效
            if (0 <= row < self.table.rowCount() and 0 <= col < self.table.columnCount()):
                # 获取或创建单元格项
                item = self.table.item(row, col)
                if not item:
                    item = QTableWidgetItem()
                    self.table.setItem(row, col, item)

                # 恢复旧值
                item.setText(str(old_value))

                # 如果有数据模型，同步更新数据库
                if hasattr(self, 'data_model') and self.data_model:
                    self._sync_cell_to_database(row, col, old_value, operation)

                return True
            else:
                self.logger.warning(f"{self.table_name}撤销单元格编辑失败: 无效的行列位置 ({row}, {col})")
                return False

        except Exception as e:
            self.logger.error(f"{self.table_name}撤销单元格编辑异常: {e}")
            return False

    def _redo_cell_edit(self, operation: dict) -> bool:
        """重做单元格编辑操作"""
        try:
            row = operation.get('row', -1)
            col = operation.get('col', -1)
            new_value = operation.get('new_value', '')

            # 检查行列是否有效
            if (0 <= row < self.table.rowCount() and 0 <= col < self.table.columnCount()):
                # 获取或创建单元格项
                item = self.table.item(row, col)
                if not item:
                    item = QTableWidgetItem()
                    self.table.setItem(row, col, item)

                # 设置新值
                item.setText(str(new_value))

                # 如果有数据模型，同步更新数据库
                if hasattr(self, 'data_model') and self.data_model:
                    self._sync_cell_to_database(row, col, new_value, operation)

                return True
            else:
                self.logger.warning(f"{self.table_name}重做单元格编辑失败: 无效的行列位置 ({row}, {col})")
                return False

        except Exception as e:
            self.logger.error(f"{self.table_name}重做单元格编辑异常: {e}")
            return False

    def _undo_table_state(self, operation: dict) -> bool:
        """撤销表格状态操作 - 子类应该重写此方法"""
        self.logger.warning(f"{self.table_name}基类不支持表格状态撤销，子类应该重写此方法")
        return False

    def _redo_table_state(self, operation: dict) -> bool:
        """重做表格状态操作 - 子类应该重写此方法"""
        self.logger.warning(f"{self.table_name}基类不支持表格状态重做，子类应该重写此方法")
        return False

    def _undo_batch_operation(self, operation: dict) -> bool:
        """撤销批量操作 - 子类应该重写此方法"""
        self.logger.warning(f"{self.table_name}基类不支持批量操作撤销，子类应该重写此方法")
        return False

    def _redo_batch_operation(self, operation: dict) -> bool:
        """重做批量操作 - 子类应该重写此方法"""
        self.logger.warning(f"{self.table_name}基类不支持批量操作重做，子类应该重写此方法")
        return False

    def _sync_cell_to_database(self, row: int, col: int, value: str, operation: dict) -> None:
        """
        同步单元格数据到数据库 - 子类可以重写此方法

        参数:
            row: 行索引
            col: 列索引
            value: 单元格值
            operation: 操作信息
        """
        # 基类默认不执行数据库同步，子类可以重写此方法
        pass

    def _notify_undo_redo_state_changed(self) -> None:
        """通知撤销重做状态变化"""
        try:
            can_undo = self.can_undo()
            can_redo = self.can_redo()

            # 发射信号（如果信号已定义）
            if hasattr(self, 'undo_redo_state_changed'):
                self.undo_redo_state_changed.emit(self.table_name, can_undo, can_redo)

        except Exception as e:
            self.logger.error(f"{self.table_name}通知撤销重做状态变化失败: {e}")

    def clear_undo_redo_history(self) -> None:
        """清空撤销重做历史"""
        try:
            self.undo_redo_manager.clear_history(self.table_name)
            self._notify_undo_redo_state_changed()
            self.logger.info(f"已清空{self.table_name}的撤销重做历史")
        except Exception as e:
            self.logger.error(f"清空{self.table_name}撤销重做历史失败: {e}")

    def get_undo_redo_status(self) -> dict:
        """获取撤销重做状态信息"""
        return self.undo_redo_manager.get_status_info(self.table_name)

    def _update_widget_value(self, widget, value):
        """更新控件的值"""
        try:
            from PyQt5.QtWidgets import QComboBox, QLineEdit, QTextEdit, QDateEdit
            from PyQt5.QtCore import QDate

            if isinstance(widget, QComboBox):
                widget.setCurrentText(str(value) if value is not None else "")
            elif isinstance(widget, QLineEdit):
                widget.setText(str(value) if value is not None else "")
            elif isinstance(widget, QTextEdit):
                widget.setPlainText(str(value) if value is not None else "")
            elif isinstance(widget, QDateEdit):
                if value:
                    try:
                        date = QDate.fromString(str(value), "yyyy-MM-dd")
                        if date.isValid():
                            widget.setDate(date)
                    except:
                        pass
        except Exception as e:
            self.logger.error(f"更新控件值失败: {e}")
