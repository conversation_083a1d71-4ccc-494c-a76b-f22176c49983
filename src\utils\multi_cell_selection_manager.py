#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
多单元格选择管理器
专门处理表格的Ctrl和Shift多选功能，并提供详细的日志记录
"""

from PyQt5.QtWidgets import QTableWidget, QApplication, QAbstractItemView
from PyQt5.QtCore import Qt, QObject, QEvent, pyqtSignal
import logging


class MultiCellSelectionManager(QObject):
    """多单元格选择管理器"""
    
    # 定义信号
    selection_changed = pyqtSignal(list)  # 选择变化信号，传递选中的单元格列表
    ctrl_selection = pyqtSignal(int, int, list)  # Ctrl选择信号，传递当前单元格和所有选中单元格
    shift_selection = pyqtSignal(int, int, int, int, list)  # Shift选择信号，传递起始和结束单元格以及选中范围
    
    def __init__(self, table_widget):
        """
        初始化多单元格选择管理器
        
        参数:
            table_widget: QTableWidget对象
        """
        super().__init__(table_widget)
        self.logger = logging.getLogger(__name__)
        self.table = table_widget
        self.last_clicked_cell = None  # 记录最后点击的单元格，用于Shift选择
        self.selection_start_cell = None  # 记录选择开始的单元格
        self.is_ctrl_selecting = False  # 是否正在进行Ctrl多选
        self.is_shift_selecting = False  # 是否正在进行Shift多选
        
        # 确保表格支持多选
        self.table.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.table.setSelectionBehavior(QAbstractItemView.SelectItems)
        
        # 连接信号
        self.table.cellClicked.connect(self.on_cell_clicked)
        self.table.itemSelectionChanged.connect(self.on_selection_changed)
        
        # 安装事件过滤器
        self.table.installEventFilter(self)
        
        self.logger.info("多单元格选择管理器已初始化")
    
    def on_cell_clicked(self, row, col):
        """
        处理单元格点击事件
        
        参数:
            row: 行索引
            col: 列索引
        """
        # 检测修饰键状态
        modifiers = QApplication.keyboardModifiers()
        ctrl_pressed = bool(modifiers & Qt.ControlModifier)
        shift_pressed = bool(modifiers & Qt.ShiftModifier)
        
        # 记录点击信息
        self.logger.info(f"单元格点击: 位置=({row}, {col}), Ctrl={ctrl_pressed}, Shift={shift_pressed}")
        
        # 处理不同的选择模式
        if ctrl_pressed:
            self._handle_ctrl_selection(row, col)
        elif shift_pressed:
            self._handle_shift_selection(row, col)
        else:
            self._handle_normal_selection(row, col)
        
        # 🔧 **关键修复**：根据选择类型更新最后点击的单元格
        if shift_pressed:
            # Shift选择：保持原来的起始点不变（除非没有起始点）
            if self.last_clicked_cell is None:
                self.last_clicked_cell = (row, col)
                self.logger.debug(f"🔧 Shift选择设置新起始点: {self.last_clicked_cell}")
            else:
                # 保持原来的起始点
                self.logger.debug(f"🔧 Shift选择保持起始点: {self.last_clicked_cell}")
        else:
            # 普通选择和Ctrl选择：更新为当前点击的单元格
            self.last_clicked_cell = (row, col)
            self.logger.debug(f"🔧 更新最后点击单元格: {self.last_clicked_cell}")
    
    def _handle_normal_selection(self, row, col):
        """
        处理普通单击选择
        
        参数:
            row: 行索引
            col: 列索引
        """
        self.is_ctrl_selecting = False
        self.is_shift_selecting = False
        self.selection_start_cell = (row, col)
        
        self.logger.info(f"普通选择: 单元格({row}, {col})")
    
    def _handle_ctrl_selection(self, row, col):
        """
        处理Ctrl多选
        
        参数:
            row: 行索引
            col: 列索引
        """
        self.is_ctrl_selecting = True
        self.is_shift_selecting = False
        
        # 获取当前选中的所有单元格
        selected_cells = self.get_selected_cells()
        
        self.logger.info(f"Ctrl多选: 点击单元格({row}, {col}), 当前选中{len(selected_cells)}个单元格")
        
        # 发射Ctrl选择信号
        self.ctrl_selection.emit(row, col, selected_cells)
    
    def _handle_shift_selection(self, row, col):
        """
        处理Shift范围选择
        
        参数:
            row: 行索引
            col: 列索引
        """
        self.is_shift_selecting = True
        self.is_ctrl_selecting = False
        
        if self.last_clicked_cell is not None:
            start_row, start_col = self.last_clicked_cell
            
            # 计算选择范围
            min_row = min(start_row, row)
            max_row = max(start_row, row)
            min_col = min(start_col, col)
            max_col = max(start_col, col)
            
            # 计算范围内的所有单元格
            range_cells = []
            for r in range(min_row, max_row + 1):
                for c in range(min_col, max_col + 1):
                    range_cells.append((r, c))
            
            self.logger.info(f"Shift范围选择: 从({start_row}, {start_col})到({row}, {col}), "
                           f"选中范围({min_row}-{max_row}, {min_col}-{max_col}), "
                           f"共{len(range_cells)}个单元格")
            
            # 发射Shift选择信号
            self.shift_selection.emit(start_row, start_col, row, col, range_cells)
        else:
            self.logger.info(f"Shift选择: 没有起始单元格，当前点击({row}, {col})")
    
    def on_selection_changed(self):
        """
        处理选择变化事件
        """
        selected_cells = self.get_selected_cells()
        
        if len(selected_cells) > 1:
            self.logger.info(f"选择变化: 当前选中{len(selected_cells)}个单元格")
            
            # 如果选中的单元格不太多，显示详细列表
            if len(selected_cells) <= 20:
                self.logger.info(f"选中单元格详情: {selected_cells}")
            else:
                # 显示选择范围
                min_row = min(cell[0] for cell in selected_cells)
                max_row = max(cell[0] for cell in selected_cells)
                min_col = min(cell[1] for cell in selected_cells)
                max_col = max(cell[1] for cell in selected_cells)
                self.logger.info(f"选中范围: 行{min_row}-{max_row}, 列{min_col}-{max_col}")
            
            # 发射选择变化信号
            self.selection_changed.emit(selected_cells)
    
    def get_selected_cells(self):
        """
        获取当前选中的单元格列表

        返回:
            选中的单元格坐标列表 [(row, col), ...]
        """
        selected_cells = []

        # 🔧 关键修复：支持SelectRows模式，正确处理包含QComboBox的行选择
        selection_model = self.table.selectionModel()
        if selection_model:
            # 获取选中的行
            selected_rows = set()
            selected_indexes = selection_model.selectedRows()
            for index in selected_indexes:
                selected_rows.add(index.row())

            # 如果有选中的行，返回这些行的所有单元格
            if selected_rows:
                for row in selected_rows:
                    for col in range(self.table.columnCount()):
                        selected_cells.append((row, col))
            else:
                # 回退到原有的单元格选择方式
                for item in self.table.selectedItems():
                    if item is not None:
                        selected_cells.append((item.row(), item.column()))
        else:
            # 回退到原有方法
            for item in self.table.selectedItems():
                if item is not None:
                    selected_cells.append((item.row(), item.column()))

        # 按行列顺序排序
        selected_cells.sort(key=lambda x: (x[0], x[1]))
        return selected_cells
    
    def eventFilter(self, watched_obj, event):
        """
        事件过滤器，处理键盘和鼠标事件
        
        参数:
            watched_obj: 被监视的对象
            event: 事件对象
            
        返回:
            True表示事件已处理，False表示事件需要进一步处理
        """
        if watched_obj == self.table:
            if event.type() == QEvent.KeyPress:
                self._handle_key_press(event)
            elif event.type() == QEvent.MouseButtonPress:
                self._handle_mouse_press(event)
        
        return super().eventFilter(watched_obj, event)
    
    def _handle_key_press(self, event):
        """
        处理键盘按下事件
        
        参数:
            event: 键盘事件
        """
        modifiers = event.modifiers()
        ctrl_pressed = bool(modifiers & Qt.ControlModifier)
        shift_pressed = bool(modifiers & Qt.ShiftModifier)
        
        key_name = self._get_key_name(event.key())
        
        # 记录按键信息
        modifier_parts = []
        if ctrl_pressed:
            modifier_parts.append("Ctrl")
        if shift_pressed:
            modifier_parts.append("Shift")
        
        if modifier_parts:
            key_combo = "+".join(modifier_parts) + "+" + key_name
        else:
            key_combo = key_name
        
        # 取消按键类通知日志打印
        # self.logger.info(f"按键事件: {key_combo}")
        
        # 如果是方向键且按下了Shift，记录扩展选择
        if shift_pressed and event.key() in [Qt.Key_Up, Qt.Key_Down, Qt.Key_Left, Qt.Key_Right]:
            selected_cells = self.get_selected_cells()
            if selected_cells:
                # 取消按键类通知日志打印
                pass
    
    def _handle_mouse_press(self, event):
        """
        处理鼠标按下事件
        
        参数:
            event: 鼠标事件
        """
        modifiers = QApplication.keyboardModifiers()
        ctrl_pressed = bool(modifiers & Qt.ControlModifier)
        shift_pressed = bool(modifiers & Qt.ShiftModifier)
        
        if ctrl_pressed or shift_pressed:
            # 获取点击位置的单元格
            item = self.table.itemAt(event.pos())
            if item:
                row, col = item.row(), item.column()
                self.logger.info(f"鼠标点击(带修饰键): 位置=({row}, {col}), Ctrl={ctrl_pressed}, Shift={shift_pressed}")
    
    def _get_key_name(self, key):
        """
        获取按键名称
        
        参数:
            key: Qt按键代码
            
        返回:
            按键名称字符串
        """
        key_names = {
            Qt.Key_Up: "Up", Qt.Key_Down: "Down", Qt.Key_Left: "Left", Qt.Key_Right: "Right",
            Qt.Key_Home: "Home", Qt.Key_End: "End", Qt.Key_PageUp: "PageUp", Qt.Key_PageDown: "PageDown",
            Qt.Key_Delete: "Delete", Qt.Key_Backspace: "Backspace", Qt.Key_Enter: "Enter",
            Qt.Key_Return: "Return", Qt.Key_Space: "Space", Qt.Key_Tab: "Tab", Qt.Key_Escape: "Escape",
            Qt.Key_A: "A", Qt.Key_C: "C", Qt.Key_V: "V", Qt.Key_X: "X", Qt.Key_Z: "Z"
        }
        return key_names.get(key, f"Key_{key}")
    
    def clear_selection(self):
        """清除所有选择"""
        self.table.clearSelection()
        self.last_clicked_cell = None
        self.selection_start_cell = None
        self.is_ctrl_selecting = False
        self.is_shift_selecting = False
        self.logger.info("已清除所有选择")
    
    def select_range(self, start_row, start_col, end_row, end_col):
        """
        选择指定范围的单元格
        
        参数:
            start_row: 起始行
            start_col: 起始列
            end_row: 结束行
            end_col: 结束列
        """
        self.table.clearSelection()
        
        min_row = min(start_row, end_row)
        max_row = max(start_row, end_row)
        min_col = min(start_col, end_col)
        max_col = max(start_col, end_col)
        
        for row in range(min_row, max_row + 1):
            for col in range(min_col, max_col + 1):
                item = self.table.item(row, col)
                if item:
                    item.setSelected(True)
        
        self.logger.info(f"选择范围: 行{min_row}-{max_row}, 列{min_col}-{max_col}")
