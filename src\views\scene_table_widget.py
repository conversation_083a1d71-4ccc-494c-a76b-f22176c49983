#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
典型场景表组件
"""

import os
import json
import logging
import pandas as pd
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QHeaderView, QAbstractItemView, QCheckBox, QComboBox, QLineEdit,
    QTextEdit, QDateEdit, QDateTimeEdit, QMessageBox, QMenu, QAction,
    QSizePolicy, QApplication
)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QDateTime
from PyQt5.QtGui import QFont

from src.models.scene import Scene
from src.views.base_table_widget import BaseTableWidget

class SceneTableWidget(BaseTableWidget):
    """
    典型场景表组件

    继承自BaseTableWidget，自动获得列公式计算功能：
    1. 列公式设置和管理
    2. 自动计算和更新
    3. 公式配置保存和加载
    4. 统一的用户交互界面
    """

    def __init__(self, parent=None):
        # 先调用父类初始化，传入表格名称
        super().__init__(table_name="典型场景表", parent=parent)

        # 启用公式功能，支持用户手动添加公式列
        self.formula_enabled = True

        self.logger = logging.getLogger(__name__)

        # 初始化数据模型
        self.scene_model = Scene()
        self.data_model = self.scene_model  # 设置基类的data_model

        # 初始化字段配置
        self.field_groups = self._load_field_groups()

        # 加载字段定义配置
        self.field_definitions = self._load_field_definitions()

        # 加载显示设置
        self.display_settings = self._load_display_settings()

        # 初始化表格状态管理器
        from src.utils.table_state_manager import TableStateManager
        self.table_state_manager = TableStateManager('scene_table')

        # 初始化选择处理器
        self._init_selection_handlers()

        # 初始化颜色管理器
        self._init_color_manager()

        # 设置右键菜单
        self.table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self._show_context_menu)

        # 加载数据
        self.load_data()

        self.logger.info("典型场景表组件初始化完成")

    def _on_cell_clicked(self, row, column):
        """重写单元格点击处理，添加场景图片查看功能"""
        try:
            # 检查是否Ctrl+单击了场景图片名称字段
            header_item = self.table.horizontalHeaderItem(column)
            if header_item and header_item.text() == "场景图片名称":
                # 检测修饰键状态
                from PyQt5.QtWidgets import QApplication
                from PyQt5.QtCore import Qt
                modifiers = QApplication.keyboardModifiers()
                ctrl_pressed = bool(modifiers & Qt.ControlModifier)

                if ctrl_pressed:
                    # Ctrl+单击打开场景图片查看器
                    self._handle_scene_image_name_click(row, column)
                    return  # 直接返回，不进行常规的单元格处理

            # 调用父类的默认处理
            super()._on_cell_clicked(row, column)

        except Exception as e:
            self.logger.error(f"处理典型场景表单元格点击失败: {e}")

    def _handle_scene_image_name_click(self, row, col):
        """处理场景图片名称字段的点击事件"""
        try:
            # 获取场景图片名称
            item = self.table.item(row, col)
            if not item:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(self, "提示", "该单元格没有内容")
                return

            image_name = item.text().strip()
            if not image_name:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(self, "提示", "场景图片名称为空，请先输入图片文件名")
                return

            # 验证场景图片路径
            from src.utils.image_config_manager import get_image_config_manager
            config_manager = get_image_config_manager()

            is_valid, error_msg, full_path = config_manager.validate_scene_image_path(image_name)

            if not is_valid:
                from PyQt5.QtWidgets import QMessageBox
                # 根据错误类型提供不同的处理建议
                if "未设置场景图片根目录" in error_msg:
                    reply = QMessageBox.question(
                        self, "场景图片路径未设置",
                        f"{error_msg}\n\n是否现在设置场景图片根目录？",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.Yes
                    )
                    if reply == QMessageBox.Yes:
                        # 获取主窗口并调用图片路径设置
                        main_window = self.window()
                        if hasattr(main_window, 'show_image_path_config'):
                            main_window.show_image_path_config()
                else:
                    QMessageBox.warning(self, "无法打开场景图片", error_msg)
                return

            # 打开场景图片查看器
            from src.utils.image_viewer import show_image
            self.logger.info(f"打开场景图片查看器: {full_path}")
            show_image(full_path, self)

        except Exception as e:
            self.logger.error(f"处理场景图片名称点击失败: {e}")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", f"打开场景图片时发生错误: {str(e)}")

    def _load_field_groups(self):
        """加载字段分组配置"""
        try:
            # 定义默认字段分组
            default_groups = {
                "单独列字段": ["id", "试验类别", "场景描述", "场景关键特征", "场景图片名称", "测试功能", "测试关注项", "修改时间"],
                "固定字段": ["经度", "纬度", "场景分类", "国家", "区域"],
                "可变字段": ["道路名称", "车库名称", "车位类型", "车位编号", "记录人", "日期", "备注"]
            }

            # 尝试从新的配置文件路径加载（字段导入功能生成的文件）
            config_path = os.path.join("config", "scene_field_groups.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)

            # 尝试从旧的字段文件构建字段分组（兼容旧系统）
            try:
                field_groups = {"单独列字段": [], "固定字段": [], "可变字段": []}

                # 加载单独列字段
                separate_path = os.path.join("config", "典型场景表单独列字段.json")
                if os.path.exists(separate_path):
                    with open(separate_path, 'r', encoding='utf-8') as f:
                        separate_data = json.load(f)
                        if separate_data and isinstance(separate_data, list) and len(separate_data) > 0:
                            field_groups["单独列字段"] = list(separate_data[0].keys())

                # 加载固定字段
                fixed_path = os.path.join("config", "典型场景表固定字段.json")
                if os.path.exists(fixed_path):
                    with open(fixed_path, 'r', encoding='utf-8') as f:
                        fixed_data = json.load(f)
                        if fixed_data and isinstance(fixed_data, list) and len(fixed_data) > 0:
                            field_groups["固定字段"] = list(fixed_data[0].keys())

                # 加载可变字段
                variable_path = os.path.join("config", "典型场景表可变字段.json")
                if os.path.exists(variable_path):
                    with open(variable_path, 'r', encoding='utf-8') as f:
                        variable_data = json.load(f)
                        if variable_data and isinstance(variable_data, list) and len(variable_data) > 0:
                            field_groups["可变字段"] = list(variable_data[0].keys())

                # 如果成功加载了任何字段，返回构建的字段分组
                if any(field_groups.values()):
                    self.logger.info("从旧的字段文件构建典型场景表字段分组成功")
                    return field_groups

            except Exception as e:
                self.logger.warning(f"从旧字段文件构建字段分组失败: {e}")

            # 兼容旧的配置文件路径
            old_config_path = os.path.join(os.getcwd(), 'config', '典型场景表字段分组.json')
            if os.path.exists(old_config_path):
                with open(old_config_path, 'r', encoding='utf-8') as f:
                    field_groups = json.load(f)

                # 验证数据结构
                if not isinstance(field_groups, dict):
                    self.logger.error(f"字段分组配置格式错误，应为字典类型，实际为: {type(field_groups)}")
                    return default_groups

                # 验证每个分组的值都是列表
                for group_name, fields in field_groups.items():
                    if not isinstance(fields, list):
                        self.logger.error(f"字段分组 '{group_name}' 的值应为列表类型，实际为: {type(fields)}")
                        field_groups[group_name] = []

                self.logger.info(f"成功加载典型场景表字段分组配置: {list(field_groups.keys())}")
                return field_groups

            self.logger.info("使用默认典型场景表字段分组配置")
            return default_groups
        except Exception as e:
            self.logger.error(f"加载典型场景表字段分组配置失败: {e}")
            return {
                "单独列字段": ["id", "试验类别", "场景描述", "场景关键特征", "场景图片名称", "测试功能", "测试关注项", "修改时间"],
                "固定字段": ["经度", "纬度", "场景分类", "国家", "区域"],
                "可变字段": ["道路名称", "车库名称", "车位类型", "车位编号", "记录人", "日期", "备注"]
            }

    def _load_field_definitions(self):
        """加载字段定义配置"""
        try:
            config_path = os.path.join(os.getcwd(), 'config', 'scene_table_field_definitions.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    field_definitions = json.load(f)
                self.logger.info(f"成功加载典型场景表字段定义配置，包含 {len(field_definitions)} 个字段")
                return field_definitions
            else:
                self.logger.warning(f"典型场景表字段定义配置文件不存在: {config_path}")
                return {}
        except Exception as e:
            self.logger.error(f"加载典型场景表字段定义配置失败: {e}")
            return {}

    def _init_selection_handlers(self):
        """初始化选择处理器"""
        try:
            # 导入表格选择处理器以支持多选、复制、粘贴和删除功能
            try:
                from src.utils.table_selection_handler import TableSelectionHandler
                self.table_selection_handler = TableSelectionHandler(self.table)
                self.logger.debug("表格选择处理器初始化成功")
            except Exception as e:
                self.logger.warning(f"表格选择处理器初始化失败: {e}")
                self.table_selection_handler = None

            # 导入多单元格选择管理器以增强Ctrl和Shift多选功能
            try:
                from src.utils.multi_cell_selection_manager import MultiCellSelectionManager
                self.multi_cell_selection_manager = MultiCellSelectionManager(self.table)
                self.logger.debug("多单元格选择管理器初始化成功")
            except Exception as e:
                self.logger.warning(f"多单元格选择管理器初始化失败: {e}")
                self.multi_cell_selection_manager = None

            # 导入行插入管理器以支持行插入功能
            try:
                from src.utils.row_insert_manager import RowInsertManager
                self.row_insert_manager = RowInsertManager(self.table, self)
                self.logger.debug("行插入管理器初始化成功")
            except Exception as e:
                self.logger.warning(f"行插入管理器初始化失败: {e}")
                self.row_insert_manager = None

            # 连接多选信号
            self._connect_multi_selection_signals()

            self.logger.info("典型场景表选择处理器初始化完成")

        except Exception as e:
            self.logger.error(f"初始化典型场景表选择处理器失败: {e}")

    def _connect_multi_selection_signals(self):
        """连接多选信号"""
        try:
            if hasattr(self, 'multi_cell_selection_manager'):
                # 检查信号是否存在并连接
                if hasattr(self.multi_cell_selection_manager, 'selection_changed'):
                    self.multi_cell_selection_manager.selection_changed.connect(self._on_selection_changed)
                    self.logger.info("典型场景表多选信号连接完成")
                else:
                    self.logger.warning("多选管理器没有selection_changed信号")
        except Exception as e:
            self.logger.error(f"连接典型场景表多选信号失败: {e}")

    def _on_selection_changed(self, selected_cells=None):
        """处理选择变化"""
        try:
            # 兼容不同的信号参数格式
            if selected_cells is None:
                selected_cells = []
            elif not isinstance(selected_cells, (list, tuple)):
                selected_cells = []

            # 可以在这里处理选择变化的逻辑
            self.logger.debug(f"典型场景表选择变化: {len(selected_cells)} 个单元格")
        except Exception as e:
            self.logger.error(f"处理典型场景表选择变化失败: {e}")

    def _init_color_manager(self):
        """初始化颜色管理器"""
        try:
            from src.utils.color_utils import TableColorManager
            self.color_manager = TableColorManager(self.table)
            self.logger.info("典型场景表颜色管理器初始化完成")
        except Exception as e:
            self.logger.error(f"初始化典型场景表颜色管理器失败: {e}")
            self.color_manager = None

    def _load_display_settings(self):
        """加载显示设置配置"""
        try:
            config_path = os.path.join(os.getcwd(), 'config', '典型场景表显示设置.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    display_settings = json.load(f)
                self.logger.info("成功加载典型场景表显示设置配置")
                return display_settings
            else:
                self.logger.warning(f"典型场景表显示设置配置文件不存在: {config_path}")
                return {"visible_fields": [], "field_order": [], "column_widths": {}}
        except Exception as e:
            self.logger.error(f"加载典型场景表显示设置配置失败: {e}")
            return {"visible_fields": [], "field_order": [], "column_widths": {}}

    def load_data(self):
        """加载典型场景数据"""
        try:
            self.logger.info("开始加载典型场景数据...")
            
            # 获取数据
            df = self.scene_model.get_all_records()
            
            if df.empty:
                self.logger.info("典型场景表暂无数据")
                self._setup_empty_table()
                return
            
            # 展开JSON字段
            expanded_df = self._expand_json_fields(df)

            # 🔧 **关键修复**：直接设置数据框，不调用基类的set_data_frame方法
            # 避免基类的刷新逻辑与典型场景表的控件创建逻辑冲突
            self.data_frame = expanded_df.copy()

            # 填充表格
            self._populate_table(expanded_df)
            
            self.logger.info(f"成功加载 {len(df)} 条典型场景记录")

            # 加载单元格颜色
            self.load_cell_colors()

            # 🔧 **关键修复**：手动发出data_calculated信号，触发基类的QComboBox事件过滤器重新安装
            self.data_calculated.emit()

            # 🔧 **关键修复**：典型场景表数据加载完成后重新安装QComboBox事件过滤器
            # 这确保新创建的QComboBox控件能够正确处理多选事件
            if hasattr(self, 'table_selection_handler') and self.table_selection_handler:
                self.table_selection_handler.force_reinstall_all_combobox_filters()
                self.logger.info(f"🔧 典型场景表数据加载完成，已重新安装QComboBox事件过滤器")

        except Exception as e:
            self.logger.error(f"加载典型场景数据失败: {e}")
            QMessageBox.critical(self, "错误", f"加载数据失败:\n{str(e)}")

    def _expand_json_fields(self, df):
        """展开JSON字段"""
        try:
            expanded_df = df.copy()

            # 🔧 关键修复：首先确保所有固定字段和可变字段的列都存在
            # 从配置文件获取所有可能的字段
            all_fixed_fields = self.field_groups.get('固定字段', [])
            all_variable_fields = self.field_groups.get('可变字段', [])

            # 获取需要添加的新字段
            new_fields = []
            for field in all_fixed_fields + all_variable_fields:
                if field not in expanded_df.columns:
                    new_fields.append(field)

            # 如果有新字段需要添加，按照正确的顺序插入
            if new_fields:
                # 获取显示设置中的字段顺序
                field_order = self.display_settings.get('field_order', [])

                # 如果没有字段顺序配置，使用默认顺序
                if not field_order:
                    # 构建默认顺序：单独列字段 + 固定字段 + 可变字段
                    field_order = (self.field_groups.get('单独列字段', []) +
                                 self.field_groups.get('固定字段', []) +
                                 self.field_groups.get('可变字段', []))

                # 为新字段创建空列，并按照正确的顺序重新排列所有列
                for field in new_fields:
                    expanded_df[field] = None

                # 重新排列列顺序，确保按照field_order的顺序
                existing_columns = expanded_df.columns.tolist()
                ordered_columns = []

                # 按照field_order添加存在的列
                for field in field_order:
                    if field in existing_columns:
                        ordered_columns.append(field)

                # 添加不在field_order中但存在于DataFrame中的列
                for col in existing_columns:
                    if col not in ordered_columns:
                        ordered_columns.append(col)

                # 重新排列DataFrame的列
                expanded_df = expanded_df[ordered_columns]

            # 展开固定字段JSON字段
            if '典型场景表固定字段' in df.columns:
                for idx, row in df.iterrows():
                    fixed_data = row['典型场景表固定字段']
                    if fixed_data and isinstance(fixed_data, str):
                        try:
                            fixed_dict = json.loads(fixed_data)
                            for key, value in fixed_dict.items():
                                expanded_df.at[idx, key] = value
                        except json.JSONDecodeError:
                            self.logger.warning(f"无法解析固定字段JSON数据: {fixed_data}")

            # 展开可变字段JSON字段
            if '典型场景表可变字段' in df.columns:
                for idx, row in df.iterrows():
                    variable_data = row['典型场景表可变字段']
                    if variable_data and isinstance(variable_data, str):
                        try:
                            variable_dict = json.loads(variable_data)
                            for key, value in variable_dict.items():
                                expanded_df.at[idx, key] = value
                        except json.JSONDecodeError:
                            self.logger.warning(f"无法解析可变字段JSON数据: {variable_data}")

            # 移除原始JSON列
            columns_to_drop = ['典型场景表固定字段', '典型场景表可变字段']
            for col in columns_to_drop:
                if col in expanded_df.columns:
                    expanded_df = expanded_df.drop(columns=[col])

            return expanded_df

        except Exception as e:
            self.logger.error(f"展开JSON字段失败: {e}")
            return df

    def _setup_empty_table(self):
        """设置空表格"""
        try:
            # 获取所有可能的字段
            all_fields = []
            if self.field_groups:
                for group_fields in self.field_groups.values():
                    all_fields.extend(group_fields)
            
            # 创建空的DataFrame
            empty_df = pd.DataFrame(columns=all_fields)
            
            # 设置数据到基类
            self.set_data_frame(empty_df)
            
            # 填充空表格
            self._populate_table(empty_df)
            
        except Exception as e:
            self.logger.error(f"设置空表格失败: {e}")

    def _populate_table(self, df):
        """填充表格数据"""
        try:
            # 获取显示设置
            visible_fields = self.display_settings.get("visible_fields", [])
            field_order = self.display_settings.get("field_order", [])
            
            # 如果没有显示设置，使用所有字段
            if not visible_fields:
                visible_fields = list(df.columns)
            
            # 确保字段顺序
            if field_order:
                # 按照配置的顺序排列字段，未配置的字段放在最后
                ordered_visible_fields = []
                for field in field_order:
                    if field in visible_fields and field in df.columns:
                        ordered_visible_fields.append(field)
                
                # 添加未在顺序中但可见的字段
                for field in visible_fields:
                    if field not in ordered_visible_fields and field in df.columns:
                        ordered_visible_fields.append(field)
            else:
                ordered_visible_fields = [field for field in visible_fields if field in df.columns]
            
            # 设置表格
            self.table.setRowCount(len(df))
            self.table.setColumnCount(len(ordered_visible_fields))  # 不添加复选框列

            # 设置表头
            headers = ordered_visible_fields
            self.table.setHorizontalHeaderLabels(headers)
            
            # 填充数据
            for row_idx, (_, row_data) in enumerate(df.iterrows()):
                # 填充列数据
                for col_idx, field in enumerate(ordered_visible_fields):
                    value = row_data.get(field, "")
                    
                    # 使用字段定义创建控件
                    widget = self._create_cell_widget(field, value, row_idx, col_idx)
                    
                    if widget is not None:
                        # 使用特殊控件
                        self.table.setCellWidget(row_idx, col_idx, widget)
                    else:
                        # 使用默认的文本单元格
                        item = QTableWidgetItem(str(value) if value is not None else "")

                        # 为"场景图片名称"字段添加特殊样式
                        if field == "场景图片名称":
                            # 设置为可点击的链接样式
                            if value and str(value).strip():
                                from PyQt5.QtGui import QBrush
                                from PyQt5.QtCore import Qt
                                item.setForeground(QBrush(Qt.blue))
                                item.setToolTip("按住Ctrl键并点击查看场景图片")
                                # 添加下划线样式
                                font = item.font()
                                font.setUnderline(True)
                                item.setFont(font)
                            else:
                                from PyQt5.QtGui import QBrush
                                from PyQt5.QtCore import Qt
                                item.setForeground(QBrush(Qt.gray))
                                item.setToolTip("未设置场景图片")

                        self.table.setItem(row_idx, col_idx, item)
            
            # 应用列可见性设置
            self._apply_column_visibility(ordered_visible_fields)
            
            # 调整列宽
            self.table.resizeColumnsToContents()
            
        except Exception as e:
            self.logger.error(f"填充表格数据失败: {e}")
            QMessageBox.critical(self, "错误", f"填充表格失败:\n{str(e)}")

    def _create_cell_widget(self, field_name, value, row=None, col=None):
        """根据字段定义创建单元格控件"""
        try:
            field_def = self.field_definitions.get(field_name, {})
            widget_type = field_def.get("type", "QLineEdit")
            options = field_def.get("options", [])
            
            if widget_type == "QComboBox":
                # 🔧 统一QComboBox样式：参考其他表格的标准样式
                # 直接创建QComboBox，不使用容器，避免布局异常
                widget = QComboBox()
                valid_options = [opt for opt in options if opt and str(opt).strip()]
                # 🔧 关键修复：添加空选项，与其他表格保持一致，允许用户清空选择
                widget.addItem("")  # 添加空选项作为第一项
                widget.addItems(valid_options)
                widget.setEditable(True)  # 🔧 关键修复：设置为可编辑，支持复制粘贴功能
                widget.setInsertPolicy(QComboBox.NoInsert)  # 🔧 关键修复：禁止插入新项，保持下拉选项不变

                # 🔧 关键修复：设置QComboBox的大小策略和约束，防止布局异常
                # 使用Expanding策略允许控件在水平方向上扩展和收缩，但保持在单元格内
                widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

                # 🔧 设置最小和最大宽度，确保控件不会过度缩小或扩展
                widget.setMinimumWidth(80)  # 增加最小宽度，确保内容可见
                widget.setMaximumWidth(300)  # 设置最大宽度，防止过度扩展

                # 🔧 设置高度自适应，与普通文本字段保持一致
                # 不设置固定高度或最大高度限制，让QComboBox完全自适应表格行高
                widget.setMinimumHeight(20)  # 设置较小的最小高度，确保基本可见性
                # 移除最大高度限制，让QComboBox能够随行高自适应调节
                widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)  # 垂直方向也使用Expanding策略

                # 🔧 设置统一样式，确保在小宽度下也能正常显示，移除外层容器框和滚动条
                widget.setStyleSheet("""
                    QComboBox {
                        border: 1px solid #ccc;
                        border-radius: 3px;
                        padding: 2px 5px;
                        background-color: white;
                        min-width: 80px;
                    }
                    QComboBox::drop-down {
                        border: none;
                        width: 20px;
                    }
                    QComboBox::down-arrow {
                        width: 12px;
                        height: 12px;
                    }
                    QComboBox QAbstractItemView {
                        border: 1px solid #d0d0d0;
                        selection-background-color: #4682B4;
                    }
                    QComboBox QAbstractItemView::item {
                        height: 25px;
                        padding: 2px 5px;
                    }
                    QComboBox QScrollBar:vertical {
                        width: 0px;
                        background: transparent;
                    }
                    QComboBox QScrollBar::handle:vertical {
                        background: transparent;
                        width: 0px;
                    }
                    QComboBox QScrollBar::add-line:vertical,
                    QComboBox QScrollBar::sub-line:vertical {
                        width: 0px;
                        height: 0px;
                        background: transparent;
                    }
                    QComboBox QScrollBar::add-page:vertical,
                    QComboBox QScrollBar::sub-page:vertical {
                        background: transparent;
                    }
                """)

                # 设置当前值
                if value:
                    widget.setCurrentText(str(value))
                else:
                    widget.setCurrentIndex(-1)  # 🔧 关键修复：空值时设置为无选择状态，允许用户清空

                # 🔧 关键修复：连接QComboBox的currentTextChanged信号
                # 这是修复QComboBox数据无法保存到数据库的核心代码
                if row is not None and col is not None:
                    widget.currentTextChanged.connect(
                        lambda text, r=row, c=col: self._on_combo_text_changed(r, c, text))

                return widget
            elif widget_type == "QTextEdit":
                widget = QTextEdit()
                widget.setMaximumHeight(60)
                if value:
                    widget.setPlainText(str(value))
                return widget
            elif widget_type == "QDateEdit":
                widget = QDateEdit()
                widget.setCalendarPopup(True)
                if value:
                    try:
                        if isinstance(value, str):
                            date = QDate.fromString(value, "yyyy-MM-dd")
                            if date.isValid():
                                widget.setDate(date)
                    except:
                        pass
                return widget
            elif widget_type == "QDateTimeEdit":
                widget = QDateTimeEdit()
                widget.setCalendarPopup(True)
                if value:
                    try:
                        if isinstance(value, str):
                            datetime = QDateTime.fromString(value, "yyyy-MM-dd hh:mm:ss")
                            if datetime.isValid():
                                widget.setDateTime(datetime)
                    except:
                        pass
                return widget
            else:
                # 对于QLineEdit和其他类型，返回None使用默认处理
                return None
                
        except Exception as e:
            self.logger.error(f"创建单元格控件失败: {e}")
            return None

    def _create_checkbox_cell_widget(self, checked=False):
        """创建复选框单元格控件"""
        checkbox = QCheckBox()
        checkbox.setChecked(checked)
        checkbox.setStyleSheet("QCheckBox { margin: auto; }")
        return checkbox

    def _apply_column_visibility(self, visible_fields):
        """应用列可见性设置"""
        try:
            # 隐藏所有列
            for col in range(self.table.columnCount()):
                self.table.setColumnHidden(col, True)

            # 显示可见字段列
            headers = [self.table.horizontalHeaderItem(i).text() for i in range(self.table.columnCount())]
            for field in visible_fields:
                if field in headers:
                    col_index = headers.index(field)
                    self.table.setColumnHidden(col_index, False)

        except Exception as e:
            self.logger.error(f"应用列可见性设置失败: {e}")

    def add_record(self, data=None, batch_mode=False):
        """
        添加新记录

        参数:
            data: 记录数据，如果为None则创建默认数据
            batch_mode: 是否为批量模式，批量模式下不会立即重新加载表格
        """
        try:
            self.logger.debug("=== 开始添加典型场景记录 ===")
            self.logger.debug(f"输入数据: {data}")
            self.logger.debug(f"字段分组类型: {type(self.field_groups)}")
            self.logger.debug(f"字段分组内容: {self.field_groups}")

            if data is None:
                # 创建默认数据
                data = {}
                self.logger.debug(f"初始化空数据字典: {data}")

                self.logger.debug(f"检查字段分组条件: self.field_groups={self.field_groups}")
                self.logger.debug(f"字段分组布尔值: {bool(self.field_groups)}")
                self.logger.debug(f"字段分组是字典: {isinstance(self.field_groups, dict)}")

                if self.field_groups and isinstance(self.field_groups, dict):
                    self.logger.debug("开始处理字段分组...")
                    for group_name, fields in self.field_groups.items():
                        self.logger.debug(f"处理分组 '{group_name}', 字段类型: {type(fields)}, 内容: {fields}")
                        # 确保fields是列表类型
                        if isinstance(fields, list):
                            for field in fields:
                                if field != "id" and field != "修改时间":
                                    self.logger.debug(f"准备添加字段: {field}, 当前data类型: {type(data)}")
                                    data[field] = ""
                                    self.logger.debug(f"添加默认字段: {field}, 添加后data类型: {type(data)}")
                        else:
                            self.logger.warning(f"字段分组 '{group_name}' 的值不是列表类型: {type(fields)}")
                    self.logger.debug(f"字段分组处理完成，数据类型: {type(data)}, 数据: {data}")
                else:
                    self.logger.warning(f"字段分组无效: {self.field_groups}")
                    # 即使字段分组无效，也要确保data是字典
                    if not isinstance(data, dict):
                        data = {}
                        self.logger.debug("重置data为空字典")

            self.logger.debug(f"最终数据类型: {type(data)}")
            self.logger.debug(f"最终数据: {data}")

            # 确保data是字典类型
            if not isinstance(data, dict):
                self.logger.error(f"数据类型错误，期望dict，实际为: {type(data)}, 值: {data}")
                # 创建一个空的字典作为默认值
                data = {}
                self.logger.debug("使用空字典作为默认数据")

            # 添加记录
            self.logger.debug("调用数据模型添加记录...")
            record_id = self.scene_model.add_record(
                data,
                field_definitions=self.field_definitions,
                field_groups=self.field_groups
            )

            if record_id:
                # 性能优化：批量模式下不重新加载表格，由调用方统一处理
                if not batch_mode:
                    self.load_data()  # 重新加载数据

                self.record_added.emit(record_id)

                # 减少日志输出频率
                if record_id % 10 == 0:
                    self.logger.info(f"添加典型场景记录成功，当前ID: {record_id}")

                return record_id
            else:
                if not batch_mode:
                    QMessageBox.warning(self, "警告", "添加记录失败")
                return None

        except Exception as e:
            self.logger.error(f"添加典型场景记录失败: {e}")
            if not batch_mode:
                QMessageBox.critical(self, "错误", f"添加记录失败:\n{str(e)}")
            return None

    def add_multiple_records(self, count):
        """
        批量添加多条记录

        参数:
            count: 要添加的记录数量

        返回:
            成功添加的记录ID列表
        """
        try:
            self.logger.info(f"开始批量添加 {count} 条典型场景记录")

            # 临时禁用表格信号，避免在批量操作期间触发单元格更新事件
            original_signals_blocked = self.table.signalsBlocked()
            self.table.blockSignals(True)

            # 设置批量操作标志
            self._updating_display = True

            added_record_ids = []

            try:
                # 批量添加记录
                for i in range(count):
                    record_id = self.add_record(data=None, batch_mode=True)
                    if record_id:
                        added_record_ids.append(record_id)
                    else:
                        self.logger.warning(f"批量添加第 {i+1} 条典型场景记录失败")

                # 批量操作完成后，统一重新加载表格
                if added_record_ids:
                    self.load_data()
                    self.logger.info(f"批量添加完成，成功添加 {len(added_record_ids)} 条典型场景记录")

                    # 发射批量添加完成信号
                    for record_id in added_record_ids:
                        self.record_added.emit(record_id)

                return added_record_ids

            finally:
                # 恢复表格信号和标志
                self._updating_display = False
                self.table.blockSignals(original_signals_blocked)

        except Exception as e:
            self.logger.error(f"批量添加典型场景记录失败: {e}")
            # 确保在异常情况下也恢复信号状态
            self._updating_display = False
            self.table.blockSignals(original_signals_blocked)
            QMessageBox.critical(self, "错误", f"批量添加记录失败: {e}")
            return []

    def update_record(self, record_id, data):
        """更新记录"""
        try:
            success = self.scene_model.update_record(
                record_id,
                data,
                field_definitions=self.field_definitions,
                field_groups=self.field_groups
            )

            if success:
                self.logger.info(f"成功更新典型场景记录，ID: {record_id}")
                self.load_data()  # 重新加载数据
                self.record_updated.emit(record_id)
                return True
            else:
                QMessageBox.warning(self, "警告", "更新记录失败")
                return False

        except Exception as e:
            self.logger.error(f"更新典型场景记录失败: {e}")
            QMessageBox.critical(self, "错误", f"更新记录失败:\n{str(e)}")
            return False

    def delete_selected_records(self):
        """删除选中的记录"""
        try:
            selected_ids = self.get_selected_records()
            if not selected_ids:
                QMessageBox.information(self, "提示", "请先选择要删除的记录")
                return False

            # 确认删除
            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要删除选中的 {len(selected_ids)} 条记录吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                deleted_count = self.scene_model.delete_records(selected_ids)
                if deleted_count > 0:
                    self.logger.info(f"成功删除 {deleted_count} 条典型场景记录")
                    self.load_data()  # 重新加载数据
                    for record_id in selected_ids:
                        self.record_deleted.emit(record_id)
                    QMessageBox.information(self, "成功", f"成功删除 {deleted_count} 条记录")
                    return True
                else:
                    QMessageBox.warning(self, "警告", "删除记录失败")
                    return False

            return False

        except Exception as e:
            self.logger.error(f"删除典型场景记录失败: {e}")
            QMessageBox.critical(self, "错误", f"删除记录失败:\n{str(e)}")
            return False

    def get_selected_records(self):
        """获取选中的记录ID列表 - 基于单元格选择"""
        try:
            selected_ids = []
            selected_rows = set()

            # 获取选中的单元格对应的行
            for item in self.table.selectedItems():
                selected_rows.add(item.row())

            # 获取这些行的ID
            for row in selected_rows:
                id_item = self.table.item(row, 0)  # ID在第0列
                if id_item and id_item.text().strip():
                    try:
                        record_id = int(id_item.text())
                        selected_ids.append(record_id)
                    except ValueError:
                        self.logger.warning(f"无法解析记录ID: {id_item.text()}")

            return selected_ids

        except Exception as e:
            self.logger.error(f"获取选中记录失败: {e}")
            return []

    def apply_display_settings(self, settings):
        """应用显示设置"""
        try:
            self.display_settings = settings
            self.load_data()  # 重新加载数据以应用新设置
            self.logger.info("成功应用典型场景表显示设置")
        except Exception as e:
            self.logger.error(f"应用显示设置失败: {e}")

    def get_table_data(self):
        """获取表格数据"""
        try:
            return self.scene_model.get_all_records()
        except Exception as e:
            self.logger.error(f"获取表格数据失败: {e}")
            return pd.DataFrame()

    def refresh_data(self):
        """刷新数据"""
        self.load_data()

    def import_field_json(self, json_data=None):
        """导入字段JSON配置"""
        try:
            # 注意：json_data参数保留用于接口兼容性，实际从配置文件重新加载

            # 重新加载字段分组配置
            self.field_groups = self._load_field_groups()

            # 重新加载字段定义配置
            self.field_definitions = self._load_field_definitions()

            # 重新加载数据以应用新的字段配置
            self.load_data()
            self.logger.info("典型场景表字段JSON配置导入成功")
        except Exception as e:
            self.logger.error(f"导入典型场景表字段JSON配置失败: {e}")
            raise

    def _show_context_menu(self, position):
        """显示增强的右键菜单，与其他表格保持一致"""
        menu = QMenu(self)

        # 创建 "新增" 的级联菜单
        add_menu = menu.addMenu("新增")
        add_blank_row_action = add_menu.addAction("新增空白行")

        # 添加行插入功能
        insert_above_action = menu.addAction("向上方插入行")
        insert_below_action = menu.addAction("向下方插入行")

        menu.addSeparator()

        # 复制、粘贴、删除操作
        copy_action = menu.addAction("复制")
        paste_action = menu.addAction("粘贴")
        paste_as_new_action = menu.addAction("粘贴为新行")
        delete_action = menu.addAction("删除")

        menu.addSeparator()

        # 创建 "导入导出" 的级联菜单
        import_export_menu = menu.addMenu("导入导出")
        import_data_action = import_export_menu.addAction("导入数据")
        export_table_action = import_export_menu.addAction("导出表格")

        # 刷新
        refresh_action = menu.addAction("刷新")

        # 检查是否有选中的单元格或行
        selected_items = self.table.selectedItems()
        has_selection = len(selected_items) > 0

        # 根据选择状态启用/禁用菜单项
        if not has_selection:
            copy_action.setEnabled(False)
            delete_action.setEnabled(False)
        else:
            copy_action.setEnabled(True)
            delete_action.setEnabled(True)

        # 检查是否有复制的数据
        has_copied_data = False
        try:
            if (hasattr(self, 'table_selection_handler') and
                self.table_selection_handler and
                hasattr(self.table_selection_handler, 'clipboard_data')):
                has_copied_data = len(self.table_selection_handler.clipboard_data) > 0
        except Exception as e:
            self.logger.debug(f"检查剪贴板数据失败: {e}")
            has_copied_data = False

        paste_action.setEnabled(has_copied_data)
        paste_as_new_action.setEnabled(has_copied_data)

        # 显示菜单并获取所选操作
        action = menu.exec_(self.table.mapToGlobal(position))

        # 处理菜单操作
        if action == add_blank_row_action:
            self.add_record()  # 调用正确的add_record方法，会在数据库中创建记录并生成ID
        elif action == insert_above_action:
            # 使用行插入管理器进行多行插入
            self.insert_rows_above()
        elif action == insert_below_action:
            # 使用行插入管理器进行多行插入
            self.insert_rows_below()
        elif action == copy_action:
            self._copy_selection()
        elif action == paste_action:
            self._paste_selection()
        elif action == paste_as_new_action:
            self._paste_as_new_row()
        elif action == delete_action:
            self.delete_selected_records()
        elif action == import_data_action:
            self._import_data()
        elif action == export_table_action:
            self._export_data()
        elif action == refresh_action:
            self.load_data()

    def delete_selected_records(self):
        """删除选中的记录"""
        try:
            selected_rows = set()
            for item in self.table.selectedItems():
                selected_rows.add(item.row())

            if not selected_rows:
                QMessageBox.warning(self, "警告", "请先选择要删除的记录")
                return

            # 确认删除
            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要删除选中的 {len(selected_rows)} 条记录吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 获取要删除的记录ID
            record_ids = []
            for row in selected_rows:
                id_item = self.table.item(row, 0)  # ID列现在在第0列
                if id_item:
                    record_ids.append(int(id_item.text()))

            # 删除记录
            deleted_count = 0
            for record_id in record_ids:
                if self.scene_model.delete_record(record_id):
                    deleted_count += 1

            if deleted_count > 0:
                self.logger.info(f"成功删除 {deleted_count} 条典型场景记录")
                self.load_data()  # 重新加载数据
                QMessageBox.information(self, "成功", f"成功删除 {deleted_count} 条记录")
            else:
                QMessageBox.warning(self, "警告", "删除记录失败")

        except Exception as e:
            self.logger.error(f"删除典型场景记录失败: {e}")
            QMessageBox.critical(self, "错误", f"删除记录失败:\n{str(e)}")

    def delete_record(self):
        """删除记录 - 基类要求的方法"""
        self.delete_selected_records()

    def export_data(self):
        """导出数据 - 基类要求的方法"""
        self._export_data()

    def _save_formula_results_to_database(self, formula_column: str, calculated_series: pd.Series):
        """将公式计算结果保存到数据库 - 优化版本，使用批量更新"""
        try:
            # 🔧 性能优化：收集所有需要更新的数据，进行批量更新
            batch_updates = []

            for row_index, value in calculated_series.items():
                record_id = self._get_record_id_for_row(row_index)
                if record_id:
                    batch_updates.append({
                        'id': record_id,
                        'data': {formula_column: value}
                    })

            if batch_updates:
                # 🔧 使用批量更新方法（如果数据模型支持）
                if hasattr(self.scene_model, 'batch_update_scene_records'):
                    success_count = self.scene_model.batch_update_scene_records(batch_updates, field_groups=self.field_groups)
                    self.logger.info(f"批量保存公式结果到数据库: {formula_column}列，成功更新{success_count}/{len(batch_updates)}条记录")
                else:
                    # 🔧 降级到逐条更新，但减少日志输出
                    success_count = 0
                    for update_item in batch_updates:
                        success = self.scene_model.update_record(update_item['id'], update_item['data'], field_groups=self.field_groups)
                        if success:
                            success_count += 1

                    # 🔧 只输出汇总日志，避免大量DEBUG日志
                    self.logger.info(f"保存公式结果到数据库: {formula_column}列，成功更新{success_count}/{len(batch_updates)}条记录")

                    # 🔧 如果有失败的记录，输出警告
                    if success_count < len(batch_updates):
                        self.logger.warning(f"部分公式结果保存失败: {formula_column}列，失败{len(batch_updates) - success_count}条记录")

        except Exception as e:
            self.logger.error(f"保存典型场景记录公式结果到数据库失败: {e}")

    def _get_record_id_for_row(self, row_index: int):
        """获取指定行的记录ID"""
        try:
            # 典型场景表的ID在第一列
            id_item = self.table.item(row_index, 0)
            if id_item and id_item.text().isdigit():
                return int(id_item.text())
            return None
        except Exception as e:
            self.logger.error(f"获取典型场景记录行{row_index}的ID失败: {e}")
            return None

    def save_formula_config(self):
        """保存公式配置"""
        try:
            if hasattr(self, 'formula_config_manager'):
                return self.formula_config_manager.save_formula_config(
                    self.table_name,
                    self.formula_engine.get_formula_configs(self.table_name)
                )
            return False
        except Exception as e:
            self.logger.error(f"保存典型场景表公式配置失败: {e}")
            return False

    def load_formula_config(self):
        """加载公式配置"""
        try:
            if hasattr(self, 'formula_config_manager'):
                configs = self.formula_config_manager.load_formula_config(self.table_name)
                if configs:
                    for column_name, formula_config in configs.items():
                        self.formula_engine.set_formula(
                            self.table_name,
                            column_name,
                            formula_config.get('formula', ''),
                            formula_config.get('format', '')
                        )
                    return True
            return False
        except Exception as e:
            self.logger.error(f"加载典型场景表公式配置失败: {e}")
            return False

    def _handle_cell_change_with_formula_update(self, row: int, column: int):
        """处理单元格变化并触发公式更新的通用方法 - 重写基类方法以添加数据库保存逻辑"""
        try:
            # 防止在更新显示时触发数据保存
            if hasattr(self, '_updating_display') and self._updating_display:
                return

            # 获取字段名
            header_item = self.table.horizontalHeaderItem(column)
            if not header_item:
                return

            field_name = header_item.text()

            # 如果是公式列，不允许直接编辑，跳过保存
            if hasattr(self, 'formula_columns') and field_name in self.formula_columns:
                self.logger.debug(f"跳过公式列 {field_name} 的数据保存")
                return

            # 获取单元格的新值
            item = self.table.item(row, column)
            if item:
                new_value = item.text()

                # 🔧 关键修复：同步数据到数据框
                self._sync_single_cell_to_dataframe(row, column, new_value)

                # 🔧 关键修复：同步数据到数据库（这是QLineEdit字段保存数据库的关键）
                self._sync_cell_to_database(row, column, new_value)

            # 如果启用了公式功能，触发公式重新计算
            if (hasattr(self, 'formula_enabled') and self.formula_enabled and
                hasattr(self, 'formula_columns') and self.formula_columns):
                self._trigger_formula_recalculation(field_name, row)

        except Exception as e:
            self.logger.error(f"处理典型场景表单元格变化失败: {e}")

    def _sync_cell_to_database(self, row: int, col: int, value: str, operation: dict = None) -> None:
        """同步单元格数据到数据库"""
        try:
            # 记录操作信息（用于撤销重做功能）
            if operation:
                self.logger.debug(f"数据库同步操作: {operation.get('type', 'unknown')}")

            # 获取记录ID
            id_item = self.table.item(row, 0)  # ID列现在在第0列
            if not id_item:
                return

            record_id_text = id_item.text()
            if not record_id_text or not record_id_text.isdigit():
                return

            record_id = int(record_id_text)

            # 获取字段名
            header_item = self.table.horizontalHeaderItem(col)
            if not header_item:
                return

            field_name = header_item.text()

            # 更新数据库
            update_data = {field_name: value}
            success = self.scene_model.update_record(
                record_id,
                update_data,
                field_definitions=self.field_definitions,
                field_groups=self.field_groups
            )

            if success:
                self.logger.debug(f"成功同步典型场景记录 {record_id} 的字段 {field_name}: {value}")
            else:
                self.logger.warning(f"同步典型场景记录 {record_id} 的字段 {field_name} 失败")

        except Exception as e:
            self.logger.error(f"同步典型场景表单元格到数据库失败: {e}")

    def insert_rows_above(self):
        """向上方插入行"""
        if hasattr(self, 'row_insert_manager') and self.row_insert_manager:
            try:
                self.row_insert_manager.insert_rows_above()
            except Exception as e:
                self.logger.error(f"行插入管理器调用失败: {e}")
                # 降级处理：直接调用新增记录
                self._insert_row_above()
        else:
            self.logger.warning("行插入管理器未初始化，使用降级处理")
            self._insert_row_above()

    def insert_rows_below(self):
        """向下方插入行"""
        if hasattr(self, 'row_insert_manager') and self.row_insert_manager:
            try:
                self.row_insert_manager.insert_rows_below()
            except Exception as e:
                self.logger.error(f"行插入管理器调用失败: {e}")
                # 降级处理：直接调用新增记录
                self._insert_row_below()
        else:
            self.logger.warning("行插入管理器未初始化，使用降级处理")
            self._insert_row_below()

    def _insert_row_above(self):
        """向上方插入行 - 实际调用add_record创建新记录"""
        self.add_record()

    def _insert_row_below(self):
        """向下方插入行 - 实际调用add_record创建新记录"""
        self.add_record()

    def _copy_selection(self):
        """复制选中内容"""
        if hasattr(self, 'table_selection_handler') and self.table_selection_handler:
            try:
                self.table_selection_handler.copy_selected_cells()
                self.logger.debug("复制选中内容成功")
            except Exception as e:
                self.logger.error(f"复制选中内容失败: {e}")
                QMessageBox.warning(self, "警告", "复制功能暂时不可用")
        else:
            self.logger.warning("表格选择处理器未初始化")
            QMessageBox.warning(self, "警告", "复制功能暂时不可用")

    def _paste_selection(self):
        """粘贴内容"""
        if hasattr(self, 'table_selection_handler') and self.table_selection_handler:
            try:
                self.table_selection_handler.paste_to_selected_cells()
                self.logger.debug("粘贴内容成功")
            except Exception as e:
                self.logger.error(f"粘贴内容失败: {e}")
                QMessageBox.warning(self, "警告", "粘贴功能暂时不可用")
        else:
            self.logger.warning("表格选择处理器未初始化")
            QMessageBox.warning(self, "警告", "粘贴功能暂时不可用")

    def _paste_as_new_row(self):
        """粘贴为新行"""
        # 这个功能需要根据具体需求实现
        QMessageBox.information(self, "提示", "粘贴为新行功能待实现")
        self.logger.info("粘贴为新行功能被调用")

    def _export_data(self):
        """导出数据"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            # 选择导出文件路径
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出典型场景表数据",
                "典型场景表数据.xlsx",
                "Excel文件 (*.xlsx);;CSV文件 (*.csv)"
            )

            if not file_path:
                return

            # 获取数据
            df = self.scene_model.get_all_records()
            if df.empty:
                QMessageBox.warning(self, "警告", "没有数据可导出")
                return

            # 展开JSON字段以便导出
            expanded_df = self._expand_json_fields(df)

            # 根据文件类型导出
            if file_path.endswith('.xlsx'):
                expanded_df.to_excel(file_path, index=False)
                self.logger.info(f"成功导出典型场景表数据到Excel: {file_path}")
                QMessageBox.information(self, "成功", f"数据已导出到:\n{file_path}")
            elif file_path.endswith('.csv'):
                expanded_df.to_csv(file_path, index=False, encoding='utf-8-sig')
                self.logger.info(f"成功导出典型场景表数据到CSV: {file_path}")
                QMessageBox.information(self, "成功", f"数据已导出到:\n{file_path}")
            else:
                QMessageBox.warning(self, "警告", "不支持的文件格式")

        except Exception as e:
            self.logger.error(f"导出典型场景表数据失败: {e}")
            QMessageBox.critical(self, "错误", f"导出数据失败:\n{str(e)}")

    def _on_combo_text_changed(self, row, column, text):
        """处理QComboBox文本变更事件"""
        try:
            # 获取记录ID
            id_item = self.table.item(row, 0)  # ID列现在在第0列
            if not id_item:
                return

            record_id_text = id_item.text()
            if not record_id_text or not record_id_text.isdigit():
                return

            record_id = int(record_id_text)

            # 获取字段名
            header_item = self.table.horizontalHeaderItem(column)
            if not header_item:
                return

            field_name = header_item.text()

            # 更新数据库
            update_data = {field_name: text}
            success = self.scene_model.update_record(
                record_id,
                update_data,
                field_definitions=self.field_definitions,
                field_groups=self.field_groups
            )

            if success:
                self.logger.debug(f"成功更新典型场景记录 {record_id} 的字段 {field_name}: {text}")
            else:
                self.logger.warning(f"更新典型场景记录 {record_id} 的字段 {field_name} 失败")

        except Exception as e:
            self.logger.error(f"处理QComboBox文本变更失败: {e}")

    def save_cell_colors(self):
        """保存单元格颜色到配置文件"""
        try:
            if hasattr(self, 'color_manager') and self.color_manager:
                self.color_manager.save_cell_colors("config/cell_colors_典型场景表.json")
                self.logger.info("典型场景表单元格颜色已保存")
        except Exception as e:
            self.logger.error(f"保存典型场景表单元格颜色失败: {e}")

    def load_cell_colors(self):
        """从配置文件加载单元格颜色"""
        try:
            if hasattr(self, 'color_manager') and self.color_manager:
                self.color_manager.load_cell_colors("config/cell_colors_典型场景表.json")
                self.logger.info("典型场景表单元格颜色已加载")
        except Exception as e:
            self.logger.error(f"加载典型场景表单元格颜色失败: {e}")

    def _create_toolbar(self):
        """创建工具栏 - 重写基类方法添加导入功能"""
        from src.utils.resource_loader import ResourceLoader
        from PyQt5.QtWidgets import QToolBar, QAction

        self.toolbar = QToolBar(f"{self.table_name}工具栏")
        self.toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

        # 新增记录
        self.add_action = QAction(ResourceLoader.load_icon("add.png"), "新增记录", self)
        self.add_action.triggered.connect(self.add_record)
        self.toolbar.addAction(self.add_action)

        # 删除记录
        self.delete_action = QAction(ResourceLoader.load_icon("delete.png"), "删除记录", self)
        self.delete_action.triggered.connect(self.delete_selected_records)
        self.toolbar.addAction(self.delete_action)

        self.toolbar.addSeparator()

        # 导入数据
        self.import_action = QAction(ResourceLoader.load_icon("import.png"), "导入", self)
        self.import_action.triggered.connect(self._import_data)
        self.toolbar.addAction(self.import_action)

        # 导出数据
        self.export_action = QAction(ResourceLoader.load_icon("export.png"), "导出", self)
        self.export_action.triggered.connect(self._export_data)
        self.toolbar.addAction(self.export_action)

        self.toolbar.addSeparator()

        # 刷新数据
        self.refresh_action = QAction(ResourceLoader.load_icon("refresh.png"), "刷新", self)
        self.refresh_action.triggered.connect(self.load_data)
        self.toolbar.addAction(self.refresh_action)

    def _import_data(self):
        """导入数据"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            # 选择导入文件
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "导入典型场景表数据",
                "",
                "Excel文件 (*.xlsx *.xls);;CSV文件 (*.csv)"
            )

            if not file_path:
                return

            # 导入数据
            import pandas as pd

            # 根据文件类型读取数据
            if file_path.endswith(('.xlsx', '.xls')):
                df = pd.read_excel(file_path)
            elif file_path.endswith('.csv'):
                df = pd.read_csv(file_path, encoding='utf-8-sig')
            else:
                QMessageBox.warning(self, "警告", "不支持的文件格式")
                return

            # 验证数据格式
            if df.empty:
                QMessageBox.warning(self, "警告", "文件中没有数据")
                return

            # 导入数据到数据库
            success_count = 0
            error_count = 0

            for _, row in df.iterrows():
                try:
                    data = row.to_dict()
                    # 移除空值和NaN值
                    data = {k: v for k, v in data.items() if pd.notna(v) and str(v).strip()}

                    if data:  # 只有在有有效数据时才导入
                        record_id = self.scene_model.add_record(
                            data,
                            field_definitions=self.field_definitions,
                            field_groups=self.field_groups
                        )
                        if record_id:
                            success_count += 1
                        else:
                            error_count += 1
                    else:
                        error_count += 1

                except Exception as e:
                    self.logger.error(f"导入行数据失败: {e}")
                    error_count += 1

            # 刷新表格数据
            if success_count > 0:
                self.load_data()

            # 显示导入结果
            message = f"导入完成！\n成功导入: {success_count} 条记录"
            if error_count > 0:
                message += f"\n失败: {error_count} 条记录"

            QMessageBox.information(self, "导入结果", message)
            self.logger.info(f"典型场景表数据导入完成: 成功{success_count}条, 失败{error_count}条")

        except Exception as e:
            self.logger.error(f"导入典型场景表数据失败: {e}")
            QMessageBox.critical(self, "错误", f"导入数据失败:\n{str(e)}")
