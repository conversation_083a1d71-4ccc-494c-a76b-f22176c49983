#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import QColor
from src.views.base_table_widget import BaseTableWidget
from src.formula.column_formula_engine import ColumnFormulaEngine
from src.models.problem import Problem

class EnhancedProblemTableWidget(BaseTableWidget):
    """增强的问题表格组件 - 支持列公式计算"""
    
    def __init__(self, parent=None):
        super().__init__(table_name="问题表", parent=parent)
        
        # 初始化数据模型
        self.problem_model = Problem()
        self.data_model = self.problem_model
        
        # 初始化列公式引擎
        self.column_formula_engine = ColumnFormulaEngine()
        
        # 数据缓存
        self.data_frame = pd.DataFrame()
        
        # 公式列标识
        self.formula_columns = set()
        
        # 加载字段配置
        self.field_groups = self._load_field_groups()
        self.display_settings = self._load_display_settings()
        
        # 初始化公式功能
        self._init_formula_features()

        # 🔧 **关键修复**：初始化选择处理器以支持QComboBox多选功能
        self._init_selection_handlers()

        # 加载数据
        self.load_data()
    
    def _init_formula_features(self):
        """初始化公式功能"""
        # 扩展右键菜单
        if hasattr(self, 'context_menu'):
            self.context_menu.addSeparator()
            
            # 列公式菜单
            formula_menu = self.context_menu.addMenu("列公式")
            
            # 设置列公式
            set_formula_action = QAction("设置列公式", self)
            set_formula_action.triggered.connect(self._show_column_formula_dialog)
            formula_menu.addAction(set_formula_action)
            
            # 清除列公式
            clear_formula_action = QAction("清除列公式", self)
            clear_formula_action.triggered.connect(self._clear_column_formula)
            formula_menu.addAction(clear_formula_action)
            
            # 重新计算
            recalc_action = QAction("重新计算", self)
            recalc_action.triggered.connect(self._recalculate_formulas)
            formula_menu.addAction(recalc_action)
        
        # 设置表头右键菜单
        self.table.horizontalHeader().setContextMenuPolicy(Qt.CustomContextMenu)
        self.table.horizontalHeader().customContextMenuRequested.connect(
            self._show_header_context_menu
        )

    def _init_selection_handlers(self):
        """初始化选择处理器"""
        try:
            # 导入表格选择处理器以支持多选、复制、粘贴和删除功能
            from src.utils.table_selection_handler import TableSelectionHandler
            self.table_selection_handler = TableSelectionHandler(self.table)

            # 导入多单元格选择管理器以增强Ctrl和Shift多选功能
            from src.utils.multi_cell_selection_manager import MultiCellSelectionManager
            self.multi_cell_selection_manager = MultiCellSelectionManager(self.table)

            # 导入行插入管理器以支持行插入功能
            from src.utils.row_insert_manager import RowInsertManager
            self.row_insert_manager = RowInsertManager(self.table, self)

            self.logger.info("增强问题表选择处理器初始化完成")

        except Exception as e:
            self.logger.error(f"初始化增强问题表选择处理器失败: {e}")

    def load_data(self):
        """加载数据"""
        try:
            # 从数据模型获取数据
            problems_df = self.problem_model.get_problems()
            
            if problems_df.empty:
                self.table.setRowCount(0)
                return
            
            # 更新数据框
            self.data_frame = problems_df.copy()
            
            # 计算公式列
            if self.formula_columns:
                self.data_frame = self.column_formula_engine.calculate_all_formula_columns(
                    self.table_name, self.data_frame
                )
            
            # 更新表格显示
            self._update_table_display()
            
        except Exception as e:
            self.logger.error(f"加载数据失败: {e}")
    
    def _update_table_display(self):
        """更新表格显示"""
        if self.data_frame.empty:
            return
        
        # 设置行数和列数
        self.table.setRowCount(len(self.data_frame))
        self.table.setColumnCount(len(self.data_frame.columns))
        
        # 设置表头
        self.table.setHorizontalHeaderLabels(list(self.data_frame.columns))
        
        # 填充数据
        for row in range(len(self.data_frame)):
            for col, column_name in enumerate(self.data_frame.columns):
                value = self.data_frame.iloc[row, col]
                
                item = QTableWidgetItem(str(value) if pd.notna(value) else "")
                
                # 设置公式列样式
                if column_name in self.formula_columns:
                    item.setBackground(QColor("#f3e5f5"))
                    item.setForeground(QColor("#7b1fa2"))
                
                self.table.setItem(row, col, item)
        
        # 更新列头样式
        self._update_header_styles()

        # 🔧 **关键修复**：增强问题表显示更新完成后重新安装QComboBox事件过滤器
        # 这确保新创建的QComboBox控件能够正确处理多选事件
        if hasattr(self, 'table_selection_handler') and self.table_selection_handler:
            self.table_selection_handler.force_reinstall_all_combobox_filters()
            self.logger.info(f"🔧 增强问题表显示更新完成，已重新安装QComboBox事件过滤器")
    
    def _update_header_styles(self):
        """更新表头样式"""
        for col in range(self.table.columnCount()):
            header_item = self.table.horizontalHeaderItem(col)
            if header_item:
                column_name = header_item.text()
                if column_name in self.formula_columns:
                    header_item.setBackground(QColor("#e8f5e8"))
                    header_item.setForeground(QColor("#2e7d32"))
    
    def _show_header_context_menu(self, position):
        """显示表头右键菜单"""
        logical_index = self.table.horizontalHeader().logicalIndexAt(position)
        if logical_index >= 0:
            self.current_column_index = logical_index
            
            # 创建表头菜单
            header_menu = QMenu(self)
            
            # 获取列名
            header_item = self.table.horizontalHeaderItem(logical_index)
            column_name = header_item.text() if header_item else ""
            
            # 设置列公式
            set_formula_action = QAction(f"设置 '{column_name}' 列公式", self)
            set_formula_action.triggered.connect(self._show_column_formula_dialog)
            header_menu.addAction(set_formula_action)
            
            # 如果是公式列，添加清除选项
            if column_name in self.formula_columns:
                clear_formula_action = QAction(f"清除 '{column_name}' 列公式", self)
                clear_formula_action.triggered.connect(self._clear_column_formula)
                header_menu.addAction(clear_formula_action)
                
                view_formula_action = QAction(f"查看 '{column_name}' 列公式", self)
                view_formula_action.triggered.connect(self._view_column_formula)
                header_menu.addAction(view_formula_action)
            
            # 显示菜单
            global_pos = self.table.horizontalHeader().mapToGlobal(position)
            header_menu.exec_(global_pos)
    
    def _show_column_formula_dialog(self):
        """显示列公式对话框"""
        if not hasattr(self, 'current_column_index'):
            return
        
        column_index = self.current_column_index
        header_item = self.table.horizontalHeaderItem(column_index)
        if not header_item:
            return
        
        column_name = header_item.text()
        
        # 创建公式编辑对话框
        dialog = ColumnFormulaDialog(self)
        dialog.set_column_info(
            table_name=self.table_name,
            column_name=column_name,
            available_columns=list(self.data_frame.columns)
        )
        
        # 获取当前公式
        current_formula = self._get_column_formula(column_name)
        dialog.set_formula(current_formula)
        
        if dialog.exec_() == QDialog.Accepted:
            formula, description = dialog.get_formula_data()
            self._set_column_formula(column_name, formula, description)
    
    def _set_column_formula(self, column_name: str, formula: str, description: str):
        """设置列公式"""
        if self.column_formula_engine.set_column_formula(
            self.table_name, column_name, formula, description):
            
            # 标记为公式列
            self.formula_columns.add(column_name)
            
            # 重新计算和显示
            self._recalculate_column(column_name)
            
            self.logger.info(f"列公式设置成功: {column_name}")
            QMessageBox.information(self, "成功", f"列 '{column_name}' 的公式设置成功")
        else:
            QMessageBox.warning(self, "错误", f"列 '{column_name}' 的公式设置失败")
    
    def _clear_column_formula(self):
        """清除列公式"""
        if not hasattr(self, 'current_column_index'):
            return
        
        column_index = self.current_column_index
        header_item = self.table.horizontalHeaderItem(column_index)
        if not header_item:
            return
        
        column_name = header_item.text()
        
        if self.column_formula_engine.remove_column_formula(self.table_name, column_name):
            self.formula_columns.discard(column_name)
            self.load_data()  # 重新加载数据
            
            QMessageBox.information(self, "成功", f"列 '{column_name}' 的公式已清除")
    
    def _view_column_formula(self):
        """查看列公式"""
        if not hasattr(self, 'current_column_index'):
            return
        
        column_index = self.current_column_index
        header_item = self.table.horizontalHeaderItem(column_index)
        if not header_item:
            return
        
        column_name = header_item.text()
        formula = self._get_column_formula(column_name)
        
        if formula:
            QMessageBox.information(self, "列公式", 
                                  f"列名: {column_name}\n公式: {formula}")
    
    def _get_column_formula(self, column_name: str) -> str:
        """获取列公式"""
        column_formulas = self.column_formula_engine.get_column_formulas(self.table_name)
        if column_name in column_formulas:
            return column_formulas[column_name].formula_template
        return ""
    
    def _recalculate_column(self, column_name: str):
        """重新计算指定列"""
        if self.data_frame.empty:
            return
        
        # 计算列值
        calculated_series = self.column_formula_engine.calculate_column(
            self.table_name, column_name, self.data_frame
        )
        
        # 更新数据框
        self.data_frame[column_name] = calculated_series
        
        # 更新表格显示
        self._update_table_display()
    
    def _recalculate_formulas(self):
        """重新计算所有公式"""
        if self.formula_columns:
            # 清除缓存
            self.column_formula_engine.clear_cache(self.table_name)
            
            # 重新加载数据
            self.load_data()
            
            QMessageBox.information(self, "完成", "所有公式已重新计算")
    
    def add_record(self):
        """添加记录"""
        # 调用父类方法或实现具体逻辑
        # 添加记录后需要重新计算公式
        super().add_record() if hasattr(super(), 'add_record') else None
        if self.formula_columns:
            self.load_data()
    
    def delete_record(self):
        """删除记录"""
        # 调用父类方法或实现具体逻辑
        # 删除记录后需要重新计算公式
        super().delete_record() if hasattr(super(), 'delete_record') else None
        if self.formula_columns:
            self.load_data()
    
    def _load_field_groups(self):
        """加载字段分组配置"""
        # 实现字段分组加载逻辑
        return {}
    
    def _load_display_settings(self):
        """加载显示设置"""
        # 实现显示设置加载逻辑
        return {}


class ColumnFormulaDialog(QDialog):
    """简化的列公式编辑对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("列公式设置")
        self.setModal(True)
        self.resize(500, 300)
        
        self._init_ui()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 列信息
        self.info_label = QLabel()
        self.info_label.setStyleSheet("font-weight: bold; color: #1976D2;")
        layout.addWidget(self.info_label)
        
        # 公式输入
        form_layout = QFormLayout()
        
        self.formula_edit = QLineEdit()
        self.formula_edit.setPlaceholderText("例如: ={数量} * {单价} 或 =value({结束里程}) - value({开始里程})")
        form_layout.addRow("公式:", self.formula_edit)
        
        self.description_edit = QLineEdit()
        self.description_edit.setPlaceholderText("公式描述（可选）")
        form_layout.addRow("描述:", self.description_edit)
        
        layout.addLayout(form_layout)
        
        # 可用列
        self.columns_list = QListWidget()
        self.columns_list.itemDoubleClicked.connect(self._insert_column_ref)
        layout.addWidget(QLabel("可用列（双击插入）:"))
        layout.addWidget(self.columns_list)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        ok_button = QPushButton("确定")
        ok_button.clicked.connect(self.accept)
        button_layout.addWidget(ok_button)
        
        cancel_button = QPushButton("取消")
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(cancel_button)
        
        layout.addLayout(button_layout)
    
    def set_column_info(self, table_name: str, column_name: str, available_columns: list):
        """设置列信息"""
        self.column_name = column_name
        self.info_label.setText(f"设置列公式: {table_name}.{column_name}")
        
        # 更新可用列
        self.columns_list.clear()
        for col in available_columns:
            if col != column_name:
                self.columns_list.addItem(col)
    
    def set_formula(self, formula: str):
        """设置公式"""
        self.formula_edit.setText(formula)
    
    def get_formula_data(self):
        """获取公式数据"""
        return self.formula_edit.text(), self.description_edit.text()
    
    def _insert_column_ref(self, item):
        """插入列引用"""
        column_name = item.text()
        current_text = self.formula_edit.text()
        cursor_pos = self.formula_edit.cursorPosition()
        
        new_text = current_text[:cursor_pos] + f"{{{column_name}}}" + current_text[cursor_pos:]
        self.formula_edit.setText(new_text)
        self.formula_edit.setFocus()
